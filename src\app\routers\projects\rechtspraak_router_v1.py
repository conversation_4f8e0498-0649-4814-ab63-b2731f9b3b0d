from uuid import UUID
from typing import List, Optional

from fastapi import APIRouter, Depends, Path, Query, Request, status
from opentelemetry.propagate import extract
from opentelemetry.trace import SpanKind

from src.app.configurations.telemetry.telemetry_configs_v2 import (
    get_logger,
    get_open_telemetry_tracer,
)
from src.app.models.verdict_models_v1 import (
    VerdictSearchResponse,
)
from src.app.services.rechtspraak_search_service import (
    RechtspraakSearchService,
)

# Dependency provider – injects a singleton service instance
from src.app.configurations.dependencies import get_rechtspraak_service


api_logger = get_logger(__name__)
api_tracer = get_open_telemetry_tracer()
router = APIRouter(prefix="/{organization_id}/rechtspraak", tags=["Rechtspraak"])


@router.get(
    "/search",
    response_model=VerdictSearchResponse,
    status_code=status.HTTP_200_OK,
    summary="Search Dutch verdicts (IDs only)",
)
async def rechtspraak_search(
    request: Request,
    organization_id: UUID = Path(..., description="Organization ID (ignored by this endpoint)"),
    q: str = Query(..., min_length=3, description="Free-text query"),
    legal_areas: Optional[List[str]] = Query(None, description="Legal area filter"),
    legal_sub_areas: Optional[List[str]] = Query(None, description="Sub-area filter"),
    top: int = Query(10, ge=1, le=100, description="Max # IDs to return"),
    svc: RechtspraakSearchService = Depends(get_rechtspraak_service),
):
    """
    Returns a list of verdict IDs that match the search parameters.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            ids = await svc.search(q, legal_areas, legal_sub_areas, top)
            return VerdictSearchResponse(items=ids, count=len(ids))

    except Exception as e:
        api_logger.error("Rechtspraak search failed", exc_info=True)
        # Let FastAPI’s default exception handler turn this into a 500
        raise e
