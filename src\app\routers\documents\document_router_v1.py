"""
This module defines FastAPI routers for document operations.
"""

# Standard imports
import json
from contextlib import contextmanager
from typing import AsyncGenerator, Optional, List
from uuid import UUID


# External imports
from azure.core.exceptions import ResourceNotFoundError
from fastapi import APIRouter, Header, Path, Query, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, StreamingResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import get_current_span, SpanKind, Status, StatusCode

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v1 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.projects.completion_models_v1 import (
    CompletionOptions,
    CompletionType,
    ProjectDocumentCompletionResponse,
)

from src.app.cosmos_db_client import CosmosDBClient
from src.app.services.completion.completion_services_v1 import DocumentCompletionService
from src.app.services.retrieval.retrieval_services_v1 import RetrieverService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


@api_router.get(
    "/{organization_id}/projects/{project_id}/documents/{document_id}/description",
    description="Generates a description for a specific document.",
    operation_id="generate_document_description",
    response_description="The generated description for the document.",
    response_model=ProjectDocumentCompletionResponse,
    summary="Description for a document. ☑️",
)
async def generate_document_description(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    document_id: UUID = Path(..., description="The ID of the document."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates a description for a specific document.
    Returns the generated description as JSONResponse.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_id (UUID): The ID of the document.

    Returns:
        JSONResponse: The generated description.
    """

    try:
        with _start_tracing_span(request):
            completion_options = CompletionOptions(
                CompletionType.DESCRIPTION,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await _generate_response(
                organization_id,
                project_id,
                [document_id],
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, [document_id], "description"
        )


@api_router.get(
    "/{organization_id}/projects/{project_id}/documents/questions",
    description="Generates questions based on a specific document.",
    operation_id="generate_document_questions",
    response_description="The generated questions for the document.",
    response_model=ProjectDocumentCompletionResponse,
    summary="Questions for a document. ☑️",
)
async def generate_document_questions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    document_ids: List[UUID] = Query(
        [], description="List of document IDs to include."
    ),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates questions based on a specific document.
    Returns the generated questions as JSONResponse.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_ids (List[UUID]): The list of document IDs to include.

    Returns:
        JSONResponse: The generated questions.
    """

    try:
        with _start_tracing_span(request):
            completion_options = CompletionOptions(
                CompletionType.QUESTIONS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await _generate_response(
                organization_id,
                project_id,
                document_ids,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, document_ids, "questions")


@api_router.get(
    "/{organization_id}/projects/{project_id}/documents/strong_points",
    description="Generates strong points for a specific document.",
    operation_id="generate_document_strong_points",
    response_description="The generated strong points for the document.",
    response_model=ProjectDocumentCompletionResponse,
    summary="Strong points for a document. ☑️",
)
async def generate_document_strong_points(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    document_ids: List[UUID] = Query(
        [], description="List of document IDs to include."
    ),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates strong points for a specific document.
    Returns the generated strong points as JSONResponse.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_ids (List[UUID]): The list of document IDs to include.

    Returns:
        JSONResponse: The generated strong points.
    """

    try:
        with _start_tracing_span(request):
            completion_options = CompletionOptions(
                CompletionType.STRONG_POINTS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await _generate_response(
                organization_id,
                project_id,
                document_ids,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, document_ids, "strong points"
        )


@api_router.get(
    "/{organization_id}/projects/{project_id}/documents/summary",
    description="Generates a summary for one or more documents.",
    operation_id="generate_document_summary",
    response_description="The generated summary for the document(s).",
    response_model=ProjectDocumentCompletionResponse,
    summary="Summary for a document. ☑️",
)
async def generate_document_summary(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    document_ids: List[UUID] = Query(
        [], description="List of document IDs to include."
    ),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates a summary for a specific document.
    Returns the generated summary as JSONResponse.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_ids (List[UUID]): The list of document IDs to include.

    Returns:
        JSONResponse: The generated summary.
    """

    try:
        with _start_tracing_span(request):
            completion_options = CompletionOptions(
                CompletionType.DOCUMENT_SUMMARY,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await _generate_response(
                organization_id,
                project_id,
                document_ids,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, document_ids, "summary")


@api_router.get(
    "/{organization_id}/projects/{project_id}/documents/timeline",
    description="Generates a timeline for one or more documents.",
    operation_id="generate_document_timeline",
    response_description="The generated summary for the document(s).",
    response_model=ProjectDocumentCompletionResponse,
    summary="Timeline for a document. ☑️",
)
async def generate_document_timeline(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    document_ids: List[UUID] = Query(
        [], description="List of document IDs to include."
    ),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates a timeline for one or more documents.
    Returns the generated timeline as JSONResponse.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_ids (List(UUID)): The IDs of the document(s).

    Returns:
        JSONResponse: The generated timeline.
    """

    try:
        with _start_tracing_span(request):
            completion_options = CompletionOptions(
                CompletionType.TIMELINE,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await _generate_response(
                organization_id,
                project_id,
                document_ids,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, document_ids, "timeline")


@api_router.get(
    "/{organization_id}/projects/{project_id}/documents/weak_points",
    description="Generates weak points for a specific document.",
    operation_id="generate_document_weak_points",
    response_description="The generated weak points for the document.",
    response_model=ProjectDocumentCompletionResponse,
    summary="Weak points for a document. ☑️",
)
async def generate_document_weak_points(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    document_ids: List[UUID] = Query(
        [], description="List of document IDs to include."
    ),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> StreamingResponse | JSONResponse:
    """
    Generates weak points for a specific document.
    Returns the generated weak points as JSONResponse.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_ids (List[UUID]): The list of document IDs to include.

    Returns:
        JSONResponse: The generated weak points.
    """

    try:
        with _start_tracing_span(request):
            completion_options = CompletionOptions(
                CompletionType.WEAK_POINTS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await _generate_response(
                organization_id,
                project_id,
                document_ids,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, document_ids, "weak points"
        )


async def stream_generator(
    completion_service: DocumentCompletionService,
    input_text: str,
    completion_options: CompletionOptions,
    top: Optional[int] = None,
) -> AsyncGenerator[str, None]:
    """
    Generates a stream of responses.
    Each chunk is formatted as JSON for easy parsing by the C# client.
    Args:
        completion_service (DocumentCompletionService): The completion service instance to use for streaming.
        input_text (str): The input text used for document processing.
        completion_options (CompletionOptions): The options containing the completion type and parameters.
        top (Optional[int], optional): The number of completions to generate. Defaults to None.
    Yields:
        StreamingChunk: A chunk of the AI response as it is generated
    Raises:
        HTTPException: If an error occurs during the streaming process.
    """

    try:
        async for chunk in completion_service.get_completion_stream(
            input_text, completion_options=completion_options
        ):
            # Convert chunk to JSON that C# can easily parse
            chunk_dict = chunk.model_dump()
            yield f"{json.dumps(chunk_dict)}\n"

    except ResourceNotFoundError as e:
        error_response = {
            "error": "ResourceNotFoundError",
            "message": str(e),
            "status_code": status.HTTP_404_NOT_FOUND,
        }
        yield f"{json.dumps(error_response)}\n"
        return

    except Exception as e:
        error_response = {
            "error": "InternalServerError",
            "message": str(e),
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        }
        yield f"{json.dumps(error_response)}\n"
        return

# TODO: input text argument never appears to be used
async def _generate_response(
    organization_id: UUID,
    project_id: UUID,
    document_ids: list[UUID],
    completion_options: CompletionOptions,
    use_streaming: bool,
    input_text: str = "*",
) -> StreamingResponse | JSONResponse:
    """
    Handles document completion requests and generates responses.

    This function handles the entire process of document completion by:
    - Retrieving the document content.
    - Generating the completion using a specific service.
    - Mapping the result to the correct field in the response model.
    - Returning a structured JSON response.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.
        document_ids (List[UUID]): The IDs of the documents.
        completion_options (CompletionOptions): The options containing the completion type and parameters.
        use_streaming (bool): Whether to use streaming response.
        input_text (str, optional): The input text used for document processing (default is "*").

    Returns:
        JSONResponse: A JSON response containing the generated content along with metadata (tokens usage).
        StreamingResponse: A streaming response.

    Raises:
        Exception: If an error occurs during the completion generation, it logs the error and raises an exception.
    """

    try:

        cosmodb_client = CosmosDBClient(
            organization_id=organization_id,
            project_id=project_id,
            document_ids=document_ids
        )

        retriever_service = RetrieverService(
            organization_id=organization_id,
            project_id=project_id,
            document_ids=document_ids,
        )

        completion_service = DocumentCompletionService(cosmodb_client=cosmodb_client,
                                                       retriever_service=retriever_service)

        if use_streaming:
            return StreamingResponse(
                stream_generator(
                    completion_service,
                    input_text,
                    completion_options=completion_options,
                ),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                    "X-Accel-Buffering": "no",
                },
            )

        completion_results = await completion_service.get_completion(
            input_text, completion_type=completion_options.completion_type
        )

        # The field in the response model with the generated content.
        content_field = _get_content_field(completion_options.completion_type)
        response_data = ProjectDocumentCompletionResponse(
            id=completion_results.id,
            document_ids=document_ids,
            project_id=project_id,
            organization_id=organization_id,
            **{content_field: completion_results.content},
            prompt_tokens=completion_results.response_metadata["token_usage"][
                "prompt_tokens"
            ],
            completion_tokens=completion_results.response_metadata["token_usage"][
                "completion_tokens"
            ],
            total_tokens=completion_results.response_metadata["token_usage"][
                "total_tokens"
            ],
        )

        return JSONResponse(
            content=jsonable_encoder(response_data),
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        api_logger.error(
            "Error generating response for documents %s in project %s",
            str(document_ids),
            project_id,
            exc_info=True,
        )

        current_span = get_current_span()
        if current_span:
            current_span.set_status(Status(StatusCode.ERROR, str(e)))

        # Raise so the caller (route) can handle it
        raise e


def _get_content_field(completion_type: CompletionType) -> str:
    """
    Maps a completion type to the corresponding field in the project response model.

    Args:
        completion_type (CompletionType): The type of completion to be mapped.

    Returns:
        str: The name of the field in the response model where the generated content
        will be placed.
    """

    content_field_mapping = {
        CompletionType.DESCRIPTION: "description",
        CompletionType.QUESTIONS: "questions",
        CompletionType.STRONG_POINTS: "strengths",
        CompletionType.DOCUMENT_SUMMARY: "summary",
        CompletionType.WEAK_POINTS: "weaknesses",
    }

    return content_field_mapping[completion_type]


def _handle_error(
    e: Exception,
    organization_id: UUID,
    project_id: UUID,
    document_ids: List[UUID],
    action: str,
):
    """
    Handles the error by logging it and raising an HTTPException with a suitable status code.

    Args:
        e (Exception): The exception that was raised.
        document_ids (List[UUID]): The list of document IDs to include.
        project_id (UUID): The ID of the project that caused the error.
        action (str): A string describing the action being attempted (e.g., 'description', 'questions').

    Raises:
        HTTPException: Raises an appropriate HTTP error.
    """

    # Log the error with additional context
    api_logger.error(
        "Error generating %s for document %s  organization %s in project %s",
        action,
        str(document_ids),
        organization_id,
        project_id,
        exc_info=True,
    )

    if isinstance(e, ResourceNotFoundError):
        api_logger.error(
            "Vector store or document for project %s and documents: %s not found.",
            project_id,
            str(document_ids),
            exc_info=True,
        )

        return JSONResponse(
            content={
                "error": "ResourceNotFoundError",
                "message": e.message,
                "status_code": status.HTTP_404_NOT_FOUND,
            },
            status_code=status.HTTP_404_NOT_FOUND,
        )

    # Raise a generic 500 error for unexpected failures, preserving the original exception
    return JSONResponse(
        content={
            "error": "InternalServerError",
            "message": f"Error generating {action} for organization {organization_id} in project {project_id}.",
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        },
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )


@contextmanager
def _start_tracing_span(request: Request):
    """
    Starts an OpenTelemetry tracing span for the given request and ensures cleanup.

    Args:
        request (Request): The FastAPI request object to derive the span name and trace context.

    Yields:
        span: The created OpenTelemetry Span for the current request.
    """
    # Extract trace context from request headers
    context = extract(dict(request.headers))

    # Start the span
    span = api_tracer.start_span(
        name=request.scope["endpoint"].__name__,
        kind=SpanKind.SERVER,
        context=context,
    )

    # Set HTTP method and URL attributes
    span.set_attribute("http.method", request.method)
    span.set_attribute("http.url", request.url.path)

    try:
        yield span
    finally:
        span.end()
