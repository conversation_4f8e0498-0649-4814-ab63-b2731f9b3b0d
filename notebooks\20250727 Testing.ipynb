{"cells": [{"cell_type": "code", "execution_count": 1, "id": "86c5f73d-0e38-434a-8cda-391661f11339", "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from azure.cosmos import CosmosClient, PartitionKey\n", "from dotenv import load_dotenv\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.messages import SystemMessage, HumanMessage\n", "from time import time\n", "from uuid import UUID\n", "\n", "os.chdir(\"C:\\\\Users\\\\<USER>\\\\OneDrive - Metyis\\\\Documenten\\\\Personal Documents\\\\LegalPA\\\\statuta-rag-api-v2\\\\\")\n", "load_dotenv(\"src/.env\")\n", "\n", "from src.app.cosmos_db_client import CosmosDBClient, _create_context_prompt_from_document\n", "from src.app.services.chat.chat_services_v1 import ChatService\n", "from src.app.services.completion.completion_services_v1 import DocumentCompletionService, ProjectCompletionService\n", "from src.app.models.chat.chat_models_v1 import ChatRequest, ChatResponse, StreamingChunk\n", "from src.app.services.rechtspraak_search_service import RechtspraakSearchService\n", "from src.app.models.projects.completion_models_v1 import (\n", "    CompletionOptions,\n", "    CompletionType,\n", ")\n", "from IPython.display import Markdown, display"]}, {"cell_type": "markdown", "id": "5ec5cc2b-4cd0-41f6-aab4-ec02f9086784", "metadata": {}, "source": ["# Test Cosmos-db Retrieval"]}, {"cell_type": "markdown", "id": "f156aee5-ff22-4d46-b95e-dbd82701946c", "metadata": {}, "source": ["### From the actual cosmos db"]}, {"cell_type": "code", "execution_count": 2, "id": "ecd28f80-9650-44dd-8eb4-10d3c7777f8e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 5 documents\n"]}, {"data": {"text/plain": ["{'id': 'bdf112b1-39c8-48e8-b154-ad0b4f109d36',\n", " 'Id': 'bdf112b1-39c8-48e8-b154-ad0b4f109d36',\n", " 'OrganizationId': '52c0808e-8d6c-4094-bcb8-b3abc582ae30',\n", " 'ProjectId': '9f8819b4-2e8e-4ba8-9a2d-26f9cb6072e4',\n", " 'Name': 'Chat_2_Rechtsgebieden_en_grenzen.docx',\n", " 'Category': 'ClientInformation',\n", " 'Type': 'ClientResponse',\n", " 'IsAnonymized': True,\n", " 'FileFormat': 1,\n", " 'PageCount': 0,\n", " 'ParagraphCount': 0,\n", " 'Paragraphs': None,\n", " '_rid': 'LLF9AK--3Y4BAAAAAAAAAA==',\n", " '_self': 'dbs/LLF9AA==/colls/LLF9AK--3Y4=/docs/LLF9AK--3Y4BAAAAAAAAAA==/',\n", " '_etag': '\"0a003bd3-0000-4700-0000-686b75860000\"',\n", " '_attachments': 'attachments/',\n", " '_ts': 1751872902}"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"org-52c0808e-8d6c-4094-bcb8-b3abc582ae30\"\n", "project_id = \"prj-9f8819b4-2e8e-4ba8-9a2d-26f9cb6072e4\"\n", "document_ids = [\"bdf112b1-39c8-48e8-b154-ad0b4f109d36\", \"94da9bdf-46eb-4523-8c4f-beed403a1579\"]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "documents = cosmosdb.get_all_documents()\n", "print(f\"Found {len(documents)} documents\")\n", "documents[0]"]}, {"cell_type": "markdown", "id": "f663eb68-4c9c-4545-b2b6-8ec297f43bad", "metadata": {}, "source": ["### Fallback to retriever for old projects"]}, {"cell_type": "code", "execution_count": 9, "id": "db8ca75e-a6f5-4c45-9795-817b214aaf3c", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found 3 documents\n"]}, {"data": {"text/plain": ["{'id': 'N2QwNDU2MWYtMDZiMy00YjM2LTgzMTktNTVmOTNiNjNjMmRl',\n", " 'OrganizationId': '46e06422-585a-4986-bf0e-f4ae9b84897e',\n", " 'ProjectId': '77344fb2-5676-49d4-8a9a-4d6454ee84cd',\n", " 'DocumentId': 'b477a048-be65-46e5-9f89-cc075c18a68f',\n", " 'Name': '240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf',\n", " 'PageCount': 9,\n", " 'Paragraphs': [{'paragraph_number_in_document': 1,\n", "   'paragraph_number_on_page': 1,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': '<PERSON><PERSON> RECHTBANK GELDERLAND Civiel recht Zittingsplaats Arnhem Zaaknummer: C/05/421257 / HA ZA 23-287 <PERSON><PERSON> van 5 juni 2024 in de zaak van jUST4SAFETY B.V., gevestigd te Tiel, eisende partij in conventie, verwerende partij in reconventie, hierna te noemen: Just4Safety, advocaat: mr<PERSON> <PERSON><PERSON>, tegen HOOIMEIJER HOLDING B.V., gevestigd te tgk875kg61, gedaagde partij in conventie, eisende partij in reconventie, hierna te noemen: Hooimeijer, advocaat: mr. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. De procedure 1 .1. Het verloop van de procedure blijkt uit: 1. het tussenvon<PERSON> van 6 september 2023 • het proces-verbaal van de mondelinge behandeling van 1 1 maart 2024. 1 .2. <PERSON> slotte is von<PERSON> bepaald. • De feiten 2.1. Op 16 oktober 2020 (na de tweede uitbraak van de coronapandemie) hebben'},\n", "  {'paragraph_number_in_document': 2,\n", "   'paragraph_number_on_page': 2,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': '<PERSON><PERSON> (hierna: <PERSON>) en <PERSON><PERSON><PERSON><PERSON> met el<PERSON><PERSON> ges<PERSON>roke<PERSON> over de gezamenlijke exploitatie van teststraten (onder de naam Coronasneltestdirect), wa<PERSON> werk<PERSON><PERSON> van bed<PERSON> of instellingen zich kunnen laten testen of zij met het coronavirus Covid-19 waren besmet. 2.2. <PERSON> en L. de Bruin (hierna: <PERSON> Bruin) waren destijds als vennoten van de vennootschap onder firma, genaamd Just4Safety V.O.F. (hierna: de VOF), actief op het gebied van medische zorghulpverlening. zaaknummer: C/05/421257 / HA ZA 23-287 5 juni 2024 2 gesprek heeft Van Wijk op verzoek van Hooimeijer een 2.3. Voorafgaand aan dit geheimhoudingsovereenkomst ondertekend. In deze overeenkomst wordt Hooimeijer aangeduid als de \"Verstrekkende Partij\" en worden <PERSON> en De Bruin gez<PERSON>nli<PERSON> \"de Geheimhouder\" genoemd. onder meer de'},\n", "  {'paragraph_number_in_document': 3,\n", "   'paragraph_number_on_page': 3,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': 'volgende passages voor: In deze overeenkomst komen VOLGENDE IN AANMERKING\" \"NEMEN HET • dat de Verstrekkende Partij een ondernemingsplan heelt ontwikkeld, volgens welk plan bedrijven en instellingen hun werknemers/medewerkers op korte termijn en op snelle wijze (Covid-19), wa<PERSON><PERSON> kunnen laten testen op een eventuele besmetting niet het Coronavirus worden gemaakt. de testuitslag binnen circa 15 minuten bekend kan kader van de uitvoering van dit plan in overleg is • dat de Verstrekkende Partij in het getreden met de heer <PERSON> van Wijk van Just4safety (mede h.o.d.n. Just4Care), welke in onderneming zich richt op het verlenen van gezondheidszorgondersteunende diensten en enige gelieerde dat kader in staat is verpleegkundigen aan de Verstrekkende Partij (of uitvoering van het ondernemingsplan c. onderne<PERSON>) ter beschikking'},\n", "  {'paragraph_number_in_document': 4,\n", "   'paragraph_number_on_page': 4,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': 'te stellen voor de q. onderneming als hierboven beschreven. de exploitatie van de heer Van <PERSON>jk evenwel tevens heeft kenbaar gemaakt dat hij bij de uitvoering van • dat cie ondernemingsplan betrokken wenst te worden en in de uitvoering van het het ondernemingsplan wenst te participeren. kader aan cle Geheimhouder infbrmatie te • dat de Verstrekkende Partij bereid is in dat zij er belang bij heelt dat cie door haar verstrekte informatie volledig verstrekken, maar dat wordt gehouden en cie Geheimhouder van clie verstrekte informatie geen gebruik of geheim misbruik zal maken ten eigen of andermans nutte. KOMEN OVEREEN ALS VOLGT: 2. Vertrouwelijke informatie erkent dat hem strikte geheimhouding is opgelegd, zowel tijdens als na Geheimhouder beëindiging van de uitwisseling'},\n", "  {'paragraph_number_in_document': 5,\n", "   'paragraph_number_on_page': 5,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': 'van informatie over het ondernemingsplan, ter zake van alle gegeven<PERSON>, infOrmatie en/of bijzonderheden die (1) direct of indirect aan <PERSON><PERSON><PERSON>houder zijn of worden verstrekt: en/of Partij; en/of (2) betrekking hebben op cle onderneming van de Verstrekkende (. • .) het ondernemingsplan (4) betrekking hebben op 3. Geheimhouding volledig geheimhouden. Onder 2.1 De geheimhouder zal alle Vertrouwelijke infOrmatie geval verstaan dat (...) geheimhouden wordt in dit verband in ieder Verstrekkende Partij (mogelijk) concurrerende activiteiten (3) Geheimhouder geen niet de verrichten (...) mag 4. Boetebeding genoemd in deze 5.1 Indien Geheimhouder (...) enige verplichting schendt, verbeurt de Geheimhouder jegens de Verstrekkende geheimhoudingsovereenkomst niet voor matig vatbare boete van C 100.000,--, waarbij de Verstrekkende Partij Partij een recht heeft op een aanvullende (schade)'},\n", "  {'paragraph_number_in_document': 6,\n", "   'paragraph_number_on_page': 6,\n", "   'page_start': 1,\n", "   'multipage': <PERSON><PERSON><PERSON>,\n", "   'text': 'vergoeding indien de door haar geleden schade het ten gevolge van een inbreuk op deze geheimhoudingsovereenkoinst groter blijkt te zijn (...).\" zaaknummer: C/05/421257 / HA ZA 23-287 5 juni 2024 3 2.4. De VOF en Hooimeijer zijn tot overeenstemming gekomen over hun samenwerking, dit onder de naam Coronasneltestdirect. De VOF was da<PERSON><PERSON> verant<PERSON><PERSON>jk voor alles wat nodig was voor het feitelijk afnemen van de coronatesten, terwijl Hooimeijer verantwoordelijk was voor de 5. Page 1 of 9'}]}"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "documents = cosmosdb.get_all_documents()\n", "print(f\"Found {len(documents)} documents\")\n", "documents[0]"]}, {"cell_type": "code", "execution_count": 11, "id": "362dd6f7-be34-4e2e-aa32-4ec1b097de69", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "            Consider the following 3 documents related to the current case that the user has provided: \n", "\n", "             Paragraph 1 on page 1 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "<PERSON>nis RECHTBANK GELDERLAND Civiel recht Zittingsplaats Arnhem Zaaknummer: C/05/421257 / HA ZA 23-287 <PERSON><PERSON> van 5 juni 2024 in de zaak van jUST4SAFETY B.V., gevestigd te Tiel, eisende partij in conventie, verwerende partij in reconventie, hierna te noemen: Just4Safety, advocaat: mr<PERSON> <PERSON><PERSON>, tegen HOOIMEIJER HOLDING B.V., gevestigd te tgk875kg61, gedaagde partij in conventie, eisende partij in reconventie, hierna te noemen: Ho<PERSON>meijer, advocaat: mr. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. De procedure 1 .1. Het verloop van de procedure blijkt uit: 1. het tussenvon<PERSON> van 6 september 2023 • het proces-verbaal van de mondelinge behandeling van 1 1 maart 2024. 1 .2. <PERSON> slotte is von<PERSON> bepaald. • De feiten 2.1. Op 16 oktober 2020 (na de tweede uitbraak van de coronapandemie) hebben\n", "Paragraph 2 on page 1 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "<PERSON><PERSON> <PERSON> (hierna: <PERSON>) en <PERSON><PERSON><PERSON><PERSON> met el<PERSON><PERSON> g<PERSON><PERSON> over de gezamenlijke exploitatie van teststraten (onder de naam Coronasneltestdirect), <PERSON><PERSON> werk<PERSON><PERSON> van bedri<PERSON> of instellingen zich kunnen laten testen of zij met het coronavirus Covid-19 waren besmet. 2.2. <PERSON> en L. de Bruin (hierna: <PERSON> B<PERSON>in) waren destijds als vennoten van de vennootschap onder firma, genaamd Just4Safety V.O.F. (hierna: de VOF), actief op het gebied van medische zorghulpverlening. zaaknummer: C/05/421257 / HA ZA 23-287 5 juni 2024 2 gesprek heeft Van Wijk op verzoek van Hooimeijer een 2.3. Voorafgaand aan dit geheimhoudingsovereenkomst ondertekend. In deze overeenkomst wordt Hooimeijer aangeduid als de \"Verstrekkende Partij\" en worden Van Wi<PERSON> en De Bruin gez<PERSON> \"de Geheimhouder\" genoemd. onder meer de\n", "Paragraph 3 on page 1 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "volgende passages voor: In deze overeenkomst komen VOLGENDE IN AANMERKING\" \"NEMEN HET • dat de Verstrekkende Partij een ondernemingsplan heelt ontwikkeld, volgens welk plan bedrijven en instellingen hun werknemers/medewerkers op korte termijn en op snelle wijze (Covid-19), wa<PERSON><PERSON> kunnen laten testen op een eventuele besmetting niet het Coronavirus worden gemaakt. de testuitslag binnen circa 15 minuten bekend kan kader van de uitvoering van dit plan in overleg is • dat de Verstrekkende Partij in het getreden met de heer <PERSON> Wijk van Just4safety (mede h.o.d.n. Just4Care), welke in onderneming zich richt op het verlenen van gezondheidszorgondersteunende diensten en enige gelieerde dat kader in staat is verpleegkundigen aan de Verstrekkende Partij (of uitvoering van het ondernemingsplan c. onderneming) ter beschikking\n", "Paragraph 4 on page 1 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "te stellen voor de q. onderneming als hierboven beschreven. de exploitatie van de heer Van Wijk evenwel tevens heeft kenbaar gemaakt dat hij bij de uitvoering van • dat cie ondernemingsplan betrokken wenst te worden en in de uitvoering van het het ondernemingsplan wenst te participeren. kader aan cle Geheimhouder infbrmatie te • dat de Verstrekkende Partij bereid is in dat zij er belang bij heelt dat cie door haar verstrekte informatie volledig verstrekken, maar dat wordt gehouden en cie Geheimhouder van clie verstrekte informatie geen gebruik of geheim misbruik zal maken ten eigen of anderman<PERSON> nutte. KOMEN OVEREEN ALS VOLGT: 2. Vertrouwelijke informatie erkent dat hem strikte geheimhouding is opgelegd, zowel tijdens als na Geheimhouder beëindiging van de uitwisseling\n", "Paragraph 5 on page 1 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "van informatie over het ondernemingsp<PERSON>, ter zake van alle gegeven<PERSON>, infOrmatie en/of bijzonderheden die (1) direct of indirect aan <PERSON><PERSON><PERSON>hou<PERSON> zijn of worden verstrekt: en/of Partij; en/of (2) betrekking hebben op cle onderneming van de Verstrekkende (. • .) het ondernemingsplan (4) betrekking hebben op 3. Geheimhouding volledig geheimhouden. Onder 2.1 De geheimhouder zal alle Vertrouwelijke infOrmatie geval verstaan dat (...) geheimhouden wordt in dit verband in ieder Verstrekkende Partij (mogelijk) concurrerende activiteiten (3) Geheimhouder geen niet de verrichten (...) mag 4. Boetebeding genoemd in deze 5.1 Indien Geheimhouder (...) enige verplichting schendt, verbeurt de Geheimhouder jegens de Verstrekkende geheimhoudingsovereenkomst niet voor matig vatbare boete van C 100.000,--, waarbij de Verstrekkende Partij Partij een recht heeft op een aanvullende (schade)\n", "Paragraph 6 on page 1 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "vergoeding indien de door haar geleden schade het ten gevolge van een inbreuk op deze geheimhoudingsovereenkoinst groter blijkt te zijn (...).\" zaaknummer: C/05/421257 / HA ZA 23-287 5 juni 2024 3 2.4. De VOF en Hooimeijer zijn tot overeenstemming gekomen over hun samenwerking, dit onder de naam Coronasneltestdirect. De VOF was daarin verantwoordelijk voor alles wat nodig was voor het feitelijk afnemen van de coronatesten, terwi<PERSON><PERSON> Hooimeijer verantwoordelijk was voor de 5. Page 1 of 9\n", "\n", " Paragraph 1 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "administratie, de financiën en de contracten. Afgesproken werd dat de opbrengsten van Coronasneltestdirect na aftrek van operationele kosten op 50-50% basis tussen de VOF en Hooimeijer zouden worden verdeeld. <PERSON><PERSON> <PERSON>i<PERSON> in het kader van de samenwerking tussen de VOF en Hooimeijer in vier plaatsen in Nederland teststraten opgezet. 2.5. Voor iedere afgenomen sneltest kon subsidie worden verkregen van het Ministerie van Volksgezondheid, Welzijn en Sport. Daarvoor was wel de tussenkomst van een bedrijfsarts of een arbodienst vereist. Hooimeijer heeft daarom op 27 februari 2021 een over<PERSON><PERSON><PERSON><PERSON>, genaamd Antigeen Sneltesten Covid-19 (SARS-COV-2)', gesloten met NexusLabor B.V. (hierna: NexusLabor). Hooimeijer verplichtte zich in dit verband jegens NexusLabor tot \"het uitvoeren van de Covid-I9 Antigeen sneltest door uw bedrijf aan medewerkers van\n", "Paragraph 2 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "verschillende bedrijven, onderwijs -instellingen, zorginstellingen, kinderopvang en zzpers\". NexusLabor verzorgde (via CONVAD B.V. die een bedrijfsarts had ingehuurd) de subsidieaanvraag. Per afgenomen sneltest bedroeg de subsidie E 61,06, waarvan € 50,00 aan Hooimeijer zou toekomen. 2.6. Hooimeijer heeft vervolgens overeenkomsten gesloten met werkgevers die in aanmerking wilden komen voor gesubsidieerde coronatesten voor hun werknemers. 2.7. De VOF heeft de coronatesten afgenomen. Van Wijk en De Bruin voerden de uitgevoerde testen in in een database. De database was gekoppeld aan de systemen van NexusLabor. NexusLabor kon vervolgens op basis van de door Van Wijk en De Bruin ingevoerde data de subsidie laten aanvragen. 2.8. Hooimeijer heeft samen met de heer Van de Poppe Health Improvement Company B.V. (HIC) opgericht. Hooimeijer en\n", "Paragraph 3 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "GrowthPond B.V. (<PERSON><PERSON><PERSON> middellijk bestuurder is) zijn bestuurders van HIC. HIC is ook coronateststraten gaan exploiteren en heeft eveneens (op 9 maart 2021) een overeenkomst gesloten met NexusLabor. De VOF heeft aan Van de Poppe protocollen met betrekking tot de teststaten verkocht. Van de Poppe heeft daarvoor een bedrag van € 40.000,-- aan de VOF betaald. <PERSON><PERSON><PERSON><PERSON> <PERSON> ni<PERSON> tevre<PERSON> was over de door de VOF verkochte informatie, heeft de VOF voormeld bedrag aan Van de Poppe terugbetaald. Hooimeijer heeft vervolgens aan de VOF een bedrag van E 20.000,-- betaald. 2.9. <PERSON><PERSON><PERSON><PERSON> was ontevreden over de samenwerking met NexusLabor en heeft op 26 mei 2021 een soortgelijke overeenkomst gesloten met ArboVita B.V. (hierna: ArboVita). Hooimeijer\n", "Paragraph 4 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "heeft vervolgens de met NexusLabor gesloten overeenkomst op 30 mei 2021 beëindigd. 2.10. Just4Safety is met ingang van 30 juni 2021 in de plaats van de VOF getreden met betrekking tot de door de VOF met Hooimeijer aangegane samenwerking. Bij de oprichting van Just4Safety zijn alle activa en passiva van de VOF ingebracht in Just4Safety. De VOF is per 1 juli 2021 uitgeschreven uit het handelsregister. zaaknummer: C/05/421257 / HA ZA 23-287 5 juni 2024 4 Just4 B.V. is enig bestuurder en aandeelhouder van Just4Safety. Van Wijk en De Bruin zijn bestuurders van Just4 B.V. en daarmee tevens middellijk bestuurders van Just4Safety. 2.11 . Hooimeijer heeft op 26 augustus 2021 drie facturen ad in totaal € 196.950,-- voor de ingediende\n", "Paragraph 5 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "subsidieaanvragen over mei-juli 2021 (voor 3.939 uitgevoerde corona -testen) aan <PERSON>ex<PERSON> verzonden. NexusLabor heeft deze facturen niet betaald. 2.12. De samenwerking tussen Just4Safety en Hooimeijer is per 1 maart 2022 geëindigd. 2.13. <PERSON><PERSON><PERSON> is tussen Just4Safety en Hooimeijer discussie ontstaan over de financiële afwikkeling van hun samenwerking in Coronasneltestdirect. 2.14. Omdat NexusLabor de facturen van 26 augustus 2021 niet betaalde, heeft Hooimeijer ten laste van NexusLabor conservatoir derdenbeslag gelegd. NexusLabor heeft daarop een kort geding tegen Hooimeijer aangespannen bij de voorzieningenrechter in de rechtbank Oost-Brabant. Tijdens de zitting van 17 november 2022 is tussen Hooimeijer en NexusLabor een schikking getroffen, uit hoofde waarvan <PERSON>us<PERSON>ab<PERSON> aan <PERSON> een bedrag van 167.000,-- heeft betaald. 2.15. Just4Safety heeft bij e-mail van 17 november\n", "Paragraph 6 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "2022 aan <PERSON>jer te kennen gegeven dat Hooimeijer jegens NexusLabor aanspraak had kunnen maken op betaling van een bedrag van E 171.346,50 en heeft Hooimeijer -tevergeefs- gesommeerd om de helft van dat bedrag, E 85.673,25, aan haar te betalen. 2.16. Hooimeijer heeft bij brief van 18 november 2022 jegens Van Wijk en De Bruin aanspraak gemaakt op de boete van € 100.000,00 uit de geheimhoudingsovereenkomst alsmede jegens Just4Safety aanspraak gemaakt op een bedrag van E 1 13.278,50 als voorlopige schadevergoeding, beide bedragen te betalen uiterlijk op 2 december 2022. 2.17. Just4Safety heeft bij brief van 24 november 2022 beide vorderingen van Hooimeijer van de hand gewezen en Hooimeijer wederom gesommeerd om uiterlijk op 2 december 2022 aan de sommatie van\n", "Paragraph 7 on page 2 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "17 november 2022 te voldoen. Ook aan deze sommatie heeft Hooimeijer geen gevolg gegeven. 2.18. Hooimeijer heeft op 8 augustus 2023 ten laste van Just4Safety, <PERSON> en De Bruin conservatoir (derden)beslag gelegd. Page 2 of 9\n", "\n", " Paragraph 1 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "tot het evenement te krijgen. Zij heeft die opdracht aanvaard, maar er is geen actie meer geweest daarna. Zij heeft niets met de websites gedaan. De websites waarnaar Hooimeijer verwijst zijn gemaakt door haar opdrachtgever en na haar verzoek zijn die websites offline gehaald omdat zij niet wilde dat haar naam daarop vermeld stond. Just4medical B.V. is een andere vennootschap van Van Wijk en De Bruin. Ook one2test.n1 is niet van Just4Safety. Uit de e-mail van haar boekhouder (R. Verkerk) d.d. 17 november 2023 blijkt dat er door haar na 1 maart 2022 geen omzet is behaald niet betrekking tot de (snel)teststraten, aldus Just4Safety. 4.50. Tegenover deze gemotiveerde betwisting van Just4Safety heeft Hooimeijer onvoldoende onderbouwd gesteld dat Just4Safety in de\n", "Paragraph 2 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "vijf dagen vóór 1 maart 2022 en na 1 maart 2022 onder het bereik van de geheimhoudingsovereenkomst vallende concurrerende activiteiten heeft verricht. Hooimeijer heeft meer in het bijzonder ter zitting desgevraagd niet kunnen verklaren dat Just4Safety na het beëindigen van de samenwerking (per 1 maart 2022) daadwerkelijk coronatesten heeft afgenomen. 4.51. Just4Safety heeft de boete van E 100.000,-- dan ook niet verbeurd. ArhoVita 4.52. In conventie is reeds overwogen en beslist dat Hooimeijer jegens Just4Safety aanspraak heeft op een bedrag van 5.154,03. Dit bedrag is verrekend met de vordering van Just4Safety in conventie, waarmee dit onderdeel van de vordering van Hooimeijer, voor zover deze voor toewijzing in aanmerking komt, teniet is gegaan en dus hier wordt afgewezen. Juridische kosten zaaknummer:\n", "Paragraph 3 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "C/05/421257 / HA ZA 23-287 juni 2024 5 14 4.53. <PERSON><PERSON> kosten zijn hiervoor in conventie meegenomen, door deze in mindering te brengen op de te verdelen opbrengst. <PERSON><PERSON><PERSON> hierop zal ter zake deze kosten hier afwijzing dienen te volgen. II De geklvordering van E 27.679,50 4.54. Deze vordering valt in twee onderdelen uiteen: a. € 20.000,-- ter zake van onverschuldigde betaling; b. C 7.679,50 (de helf<PERSON> van de niet geïncasseerde vordering). 4.55. Op grond van hetgeen hiervoor in conventie is overwogen wordt deze vordering in beide onderdelen afgewezen. III De geldvordering van C 2.671,22 4.56. Deze vordering ziet op door Hooimeijer gemaakte buitengerechtelijke kosten. 4.57. Nu in reconventie geen van de onder I en II bedoelde geldvorderingen voor toewijzing\n", "Paragraph 4 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "in aanmerking komt en de overige vorderingen, niet zijnde geldvordering<PERSON> (zoa<PERSON> hierna zal blijken) evenmin worden toegewezen, wordt de vordering ter zake van buitengerechtelijke kosten eveneens afgewezen. overige vorderingen 4.58. Just4Safety is (enkel) tekortgeschoten ter zake van de subsidieaanvragen over juli 2021 . Nu de schade die Hooimeijer daardoor heeft geleden door middel van verrekening met de vordering van Just4Safety is vergoed, heeft Hooimeijer geen rechtens te respecteren belang bij de hiervoor onder 3.4. a gevorderde verklaring voor recht, zodat ter zake afwijzing dient te volgen. 4.59. <PERSON><PERSON> van schending van de geheimhoudingsovereenkomst geen sprake is, ontvalt daarmee de grondslag aan de stelling van Hooimeijer dat Just4Safety heeft geprofiteerd van de wanprestatie van Van Wijk en De Bruin. Nu ook\n", "Paragraph 5 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "overigens niet van onrechtmatig handelen van Just4Safety is g<PERSON><PERSON><PERSON>, zal de hiervoor onder 3.4. b gevorderde verklaring voor recht eveneens worden afgewezen. 4.60. In het verlengde hiervan komt de hiervoor onder 3.4. e weergeven vordering ook niet voor toewijzing in aanmerking. 4.61 . In het voorgaande ligt besloten dat de door Hooimeijer gemaakte beslagkosten voor haar rekening dienen te blijven. proceskosten 4.62. <PERSON><PERSON><PERSON>jer is in het ongelijk gesteld en moet daarom de proceskosten betalen. De proceskosten van Just4Safety worden begroot op C 3.858,00 ter zake van salaris advocaat (2,00 punten x € 1.929,00). zaaknummer: C/05/421257 / HA ZA 23-287 5 juni 2024 15 4.63. De (alleen in reconventie gevorderde) wettelijke rente over de proceskosten is toewijs<PERSON>ar als na te melden.\n", "Paragraph 6 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "in conventie en in reconventie 4.64. <PERSON><PERSON><PERSON><PERSON> wordt veroordeeld in de na dit vonnis ontstane kosten als hierna in de beslissing zal worden vermeld. De beslissing De rechtbank in conventie 5.1 . veroorde<PERSON><PERSON> Hooimeijer om aan Just4Safety te betalen een bedrag van € 63.543,84 te vermeerderen met de wettelijke rente als bedoeld in artikel 6: 1 19 BW over het toegewezen bedrag, met ingang van 2 december 2022, tot de dag van volledige betaling, 5.2. veroo<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> in de proceskosten van Just4Safety voor het vastgestelde bedrag van € 8.271,73, te betalen binnen veertien dagen na aanschrijving daartoe, 5.3. verklaart dit vonnis tot zover uitvoerbaar bij voorraad, 5.4, wijst het meer of anders gevorderde af, in reconventie 5.5. verk<PERSON><PERSON> niet-ont<PERSON><PERSON>jk\n", "Paragraph 7 on page 8 in document 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf:\n", "in haar vorderingen, voor zover die mede tegen Van Wijk en De Bruin zijn g<PERSON>ht, 5.6. wij<PERSON> de vorderingen van Hooimeijer jegens Just4Safety af, 5.7. ve<PERSON><PERSON><PERSON><PERSON>meijer in de proceskosten van Just4Safety voor het vastgestelde bedrag van € 3.858,00, te betalen binnen veertien dagen na aanschrijving daartoe en te vermeerderen met de wettelijke rente indien deze 5. Page 8 of 9\n", "\n", "\n"]}], "source": ["print(cosmosdb.create_context_for_llm_prompt())"]}, {"cell_type": "markdown", "id": "1ae5a5ba-b5c3-48a9-a5f8-************", "metadata": {}, "source": ["# Test timeline"]}, {"cell_type": "markdown", "id": "15f274d6-09b2-451b-9c5f-11bd138c93c3", "metadata": {}, "source": ["### Document(s) Timeline"]}, {"cell_type": "code", "execution_count": 5, "id": "c13db98a-b619-4b34-b1d7-5de98ff37eab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating timeline took  0.5 minutes\n"]}, {"data": {"text/markdown": ["| datum      | tijd   | beschrijving                                                                                                                                                                | type                   | partijen                                                                   | relevantie                                                                                                    | opmerking                                                                            | document naam                                     |   pagina |   paragraaf |\n", "|:-----------|:-------|:----------------------------------------------------------------------------------------------------------------------------------------------------------------------------|:-----------------------|:---------------------------------------------------------------------------|:--------------------------------------------------------------------------------------------------------------|:-------------------------------------------------------------------------------------|:--------------------------------------------------|---------:|------------:|\n", "| 2020-10-16 |        | Gesprek tussen Van Wijk en Hooimeijer over de gezamenlijke exploitatie van teststraten onder de naam Coronasneltestdirect.                                                  | Gebeurtenis            | ['<PERSON><PERSON> van Wijk', 'Hooimeijer Holding B.V.']                                 | Dit mark<PERSON>t het begin van de samenwerking tussen de partijen voor het opzetten van teststraten.              | Dit vond plaats na de tweede uitbraak van de coronapandemie.                         | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2021-02-27 |        | Hooimeijer sluit een overeenkomst genaamd 'Antigeen Sneltesten Covid-19' met NexusLabor B.V. voor het verkrijgen van subsidies voor coronatesten.                           | Gebeurtenis            | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | Belangrijke stap in de samenwerking voor gesubsidieerde coronatesten.                                         |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           1 |\n", "| 2021-05-26 |        | Hooimeijer sluit een overeenkomst met ArboVita B.V. na ontevredenheid over NexusLabor.                                                                                      | Gebeurtenis            | ['Hooimeijer Holding B.V.', 'ArboVita B.V.']                               | Dit markeert een verandering in partnerschap voor het verkrijgen van subsidies.                               |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           3 |\n", "| 2021-05-30 |        | Hooimeijer beëindigt de overeenkomst met NexusLabor B.V.                                                                                                                    | Gebeurtenis            | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | Beëindiging van de samenwerking met NexusLabor vanwege ontevredenheid.                                        |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           3 |\n", "| 2021-06-30 |        | Just4Safety treedt in de plaats van de VOF met betrekking tot de samenwerking met <PERSON>oimeijer.                                                                               | Gebeurtenis            | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Transitie van de VOF naar Just4Safety in de samenwerking.                                                     | Bij de oprichting van Just4Safety zijn alle activa en passiva van de VOF ingebracht. | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\n", "| 2021-08-26 |        | Hooimeijer stuurt drie facturen ter waarde van €196.950 voor ingediende subsidies.                                                                                          | Gebeurtenis            | ['Hooimeijer Holding B.V.']                                                | Betaling en financiële afwikkeling van de subsidies.                                                          |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\n", "| 2022-03-01 |        | De samenwerking tussen Just4Safety en Hooimeijer is beëindigd.                                                                                                              | Gebeurtenis            | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit markeert het einde van de zakelijke relatie tussen de partijen.                                           |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\n", "| 2022-11-17 |        | Tijdens een zitting bij de voorzieningenrechter in de rechtbank Oost-Brabant is een schikking getroffen tussen Hooimeijer en NexusLabor. NexusLabor heeft €167.000 betaald. | Rechtszitting          | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | Belangrijk voor de financiële afwikkeling tussen de partijen.                                                 |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\n", "| 2022-11-18 |        | Hooimeijer heeft per brief aanspraak gemaakt op een boete van €100.000 uit de geheimhoudingsovereenkomst en een voorlopige schadevergoeding van €113.278,50.                | Correspondentie        | ['Hooimeijer Holding B.V.', '<PERSON><PERSON>', '<PERSON>', 'Just4Safety B.V.'] | Dit betreft een belangrijke claim van Hooimeijer tegen de andere partijen.                                    |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           6 |\n", "| 2022-11-24 |        | Just4Safety heeft per brief de vorderingen van Hooimeijer van de hand gewezen en Hooimeijer gesommeerd om aan de eerdere sommatie van 17 november 2022 te voldoen.          | Correspondentie        | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | <PERSON><PERSON>ie van Just4Safety op de claims van Hooimeijer.                                                          |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           6 |\n", "| 2022-12-02 |        | Hooimeijer wordt veroordeeld om €63.543,84 te betalen aan Just4Safety, vermeer<PERSON>d met wettelijke rente vanaf 2 december 2022.                                              | Rechterlijke Uitspraak | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.']                            | <PERSON><PERSON><PERSON><PERSON> in conventie over de financiële afwikkeling.                                                        |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           6 |\n", "| 2023-08-08 |        | Hooimeijer heeft conservatoir (derden)beslag gelegd ten laste van Just4Safety, <PERSON> en De Bruin.                                                                        | Juridische Stap        | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.', '<PERSON><PERSON> <PERSON>', '<PERSON>'] | Di<PERSON> is een juridische maatregel genomen door Hooimeijer.                                                      |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           7 |\n", "| 2023-09-06 |        | Tussenvonnis uitgesproken in de rechtszaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                                               | Rechterlijke Uitspraak | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit markeert een belangrijke tussenstap in de juridische procedure.                                           |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2024-03-11 |        | Proces-verbaal van de mondelinge behandeling in de rechtszaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                            | Rechtszitting          | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Mondelinge behandeling van de <PERSON>aa<PERSON>, wat een belangrijk onderdeel van de procedure is.                         |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2024-06-05 |        | Vonnis uitgesproken door Rechtbank Gelderland in de zaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                                 | Rechterlijke Uitspraak | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit is het von<PERSON> in de rechtszaak tussen de partijen, wat het eindpunt van de juridische procedure markeert. |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2024-06-05 |        | Hooimeijer wordt veroordeeld om de proceskosten van Just4Safety te betalen en de vorderingen jegens Just4Safety worden afgewezen.                                           | Rechterlijke Uitspraak | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.']                            | Belangrijke uitspraak die de rechtszaak afsluit.                                                              |                                                                                      | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           7 |"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "#documents = cosmosdb.get_all_documents()\n", "#context_list = cosmosdb.create_context_for_iterative_llm_prompting()\n", "\n", "t0 = time()\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await document_service.get_completion(input_text='', completion_type=CompletionType.TIMELINE)\n", "print(f\"Generating timeline took {(time() - t0) / 60: .1f} minutes\")\n", "display(Markdown(foo.content))"]}, {"cell_type": "markdown", "id": "398e6408-9c2e-4136-9248-704c1db9e7e9", "metadata": {}, "source": ["### Project Timeline"]}, {"cell_type": "code", "execution_count": 6, "id": "59801261-f599-41cb-87d8-5997adecb4fc", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating timeline took  0.6 minutes\n"]}, {"data": {"text/markdown": ["| datum      | tijd   | beschrijving                                                                                                                                                                              | type                   | partijen                                                                   | relevantie                                                                                 | opmerking   | document naam                                     |   pagina |   paragraaf |\n", "|:-----------|:-------|:------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|:-----------------------|:---------------------------------------------------------------------------|:-------------------------------------------------------------------------------------------|:------------|:--------------------------------------------------|---------:|------------:|\n", "| 2020-10-16 |        | Gesprek tussen Van Wijk en Hooimeijer over de gezamenlijke exploitatie van teststraten onder de naam Coronasneltestdirect.                                                                | Gebeurtenis            | ['<PERSON><PERSON> van Wijk', 'Hooimeijer Holding B.V.']                                 | Dit is het beginpunt van de samenwerking tussen de partijen.                               |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           2 |\n", "| 2021-02-27 |        | Hooimeijer sluit een overeenkomst met NexusLabor B.V. genaamd 'Antigeen Sneltesten Covid-19'.                                                                                             | Juridische Stap        | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | <PERSON><PERSON> overeen<PERSON>t was essentieel voor het verkrijgen van subsidies voor de coronatesten.   |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           1 |\n", "| 2021-05-26 |        | Hooimeijer sluit een soortgelijke overeenkomst als met NexusLabor met ArboVita B.V.                                                                                                       | Juridische Stap        | ['Hooimeijer Holding B.V.', 'ArboVita B.V.']                               | <PERSON><PERSON> overeenkomst markeert een wijziging in de samenwerking van Hooimeijer.                |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           3 |\n", "| 2021-05-30 |        | Hooimeijer beëindigt de overeenkomst met NexusLabor B.V.                                                                                                                                  | Juridische Stap        | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | Di<PERSON> markeert het einde van de samenwerking tussen Hooimeijer en NexusLabor.                |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\n", "| 2021-06-30 |        | Just4Safety treedt in de plaats van de VOF in de samenwerking met Hooimeijer.                                                                                                             | Gebeurtenis            | ['Just4Safety B.V.', 'VOF', 'Hooimeijer Holding B.V.']                     | Dit markeert een belangrijke structurele verandering in de samenwerking.                   |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\n", "| 2021-08-26 |        | Hooimeijer legt conservatoir derdenbeslag ten laste van NexusLabor vanwege onbetaalde facturen.                                                                                           | Juridische Stap        | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | Dit markeert een escalatie in de financiële geschillen tussen Hooimeijer en NexusLabor.    |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\n", "| 2022-03-01 |        | De samenwerking tussen Just4Safety en Hooimeijer is beëindigd.                                                                                                                            | Gebeurtenis            | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit markeert het einde van de zakelijke relatie tussen beide partijen.                     |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\n", "| 2022-11-17 |        | Tijdens een zitting bij de rechtbank Oost-Brabant treffen Hooimeijer en NexusLabor een schikking waarbij NexusLabor € 167.000 betaalt aan <PERSON>.                                     | Rechtszitting          | ['Hooimeijer Holding B.V.', 'NexusLabor B.V.']                             | <PERSON> <PERSON>chik<PERSON> lost een deel van de financiële geschillen tussen de partijen op.             |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\n", "| 2022-11-18 |        | Hooimeijer maakt jegens Van Wijk en De Bruin aanspraak op een boete van € 100.000 uit de geheimhoudingsovereenkomst en eist € 113.278,50 van Just4Safety als voorlopige schadevergoeding. | Correspondentie        | ['Hooimeijer Holding B.V.', '<PERSON><PERSON>', 'De Bruin', 'Just4Safety B.V.'] | Dit markeert een nieuwe stap in de juridische en financiële conflicten tussen de partijen. |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           6 |\n", "| 2022-11-24 |        | Just4Safety wijst de vorderingen van Hooimeijer af en sommeert Hooimeijer om aan haar eisen te voldoen.                                                                                   | Correspondentie        | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit versterkt de juridische strijd tussen de partijen.                                     |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           7 |\n", "| 2023-08-08 |        | Hooimeijer legt conservatoir derdenbeslag ten laste van Just4Safety, Van Wijk en De Bruin.                                                                                                | Juridische Stap        | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.', '<PERSON><PERSON>', '<PERSON> B<PERSON>'] | Dit markeert een verdere escalatie in de juridische conflicten.                            |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           7 |\n", "| 2023-09-06 |        | Tussenvonnis uitgesproken door de Rechtbank Gelderland in de zaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                                      | Rechterlijke Uitspraak | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit markeert een belangrijke stap in de juridische procedure.                              |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2024-03-11 |        | Mondelinge behandeling van de zaak tussen Just4Safety B.V. en Hooimeijer Holding B.V. vond plaats.                                                                                        | Rechtszitting          | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Mondelinge behandeling is een essentieel onderdeel van de juridische procedure.            |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2024-06-05 |        | Eindvonnis uitgesproken door de Rechtbank Gelderland in de zaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                                        | Rechterlijke Uitspraak | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']                            | Dit markeert het einde van de juridische procedure.                                        |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\n", "| 2024-06-05 |        | Rechtbank veroordeelt Hooimeijer om € 63.543,84 te betalen aan Just4Safety, vermeer<PERSON>d met wettelijke rente vanaf 2 december 2022.                                                       | Rechterlijke Uitspraak | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.']                            | Dit markeert een juridische beslissing die financiële consequenties heeft voor Hooimeijer. |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           6 |\n", "| 2024-06-05 |        | Rechtbank veroordeelt Hooimeijer in de proceskosten van Just4Safety voor € 8.271,73, te betalen binnen veertien dagen.                                                                    | Rechterlijke Uitspraak | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.']                            | De proceskosten verhogen de financiële lasten voor Hooimeijer.                             |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           6 |\n", "| 2024-06-05 |        | Rechtbank wijst de vorderingen van Hooimeijer jegens Just4Safety af.                                                                                                                      | Rechterlijke Uitspraak | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.']                            | Deze uitspraak wijst de vorderingen van Hooimeijer af, wat voordelig is voor Just4Safety.  |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           7 |\n", "| 2024-06-05 |        | Rechtbank veroordeelt Hooimeijer in de proceskosten van Just4Safety voor € 3.858,00, te betalen binnen veertien dagen.                                                                    | Rechterlijke Uitspraak | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.']                            | De uitspraak legt extra financiële lasten op Hooimeijer.                                   |             | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           7 |"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from time import time\n", "t0 = time()\n", "project_service = ProjectCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await project_service.get_completion(input_text='', completion_options=CompletionOptions(completion_type=CompletionType.TIMELINE))\n", "print(f\"Generating timeline took {(time() - t0) / 60: .1f} minutes\")\n", "display(Markdown(foo.content))\n"]}, {"cell_type": "markdown", "id": "0074591a-5be5-42b7-8c64-5205b58f8664", "metadata": {}, "source": ["## Streaming Completions"]}, {"cell_type": "markdown", "id": "149b1e2b-d080-4e18-a202-18ece7e0bbac", "metadata": {}, "source": ["### Streaming multiple documnents summary"]}, {"cell_type": "code", "execution_count": 7, "id": "17f4f790-f7da-498e-9fa2-3b4e89d03ca2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["### <PERSON><PERSON><PERSON><PERSON> van het document\n", "\n", "#### **Hoofdidee**\n", "De kern van dit document draait om de juridische geschillen tussen Hooimeijer Holding B.V. en Just4Safety B.V. Het betreft een zaak waarin Hooimeijer Holding B.V. beweert dat Just4Safety en haar gelieerde partijen inbreuk maken op een geheimhoudingsovereenkomst en andere contractuele verplichtingen, waaronder het aanbieden van coronasneltesten op diverse websites en het gebruik van bedrijfsinformatie van Hooimeijer. Daarnaast worden verschillende aanvullende producties overgelegd ter ondersteuning van Hooimeijers standpunten.\n", "\n", "---\n", "\n", "#### **Belangrijkste punten**\n", "1. **Geheimhoudingsovereenkomst**:  \n", "   - Hooimeijer Holding B.V. stelt dat Just4Safety en haar gelieerde partijen, wa<PERSON><PERSON> en De Bruin, de geheimhoudingsovereenkomst schenden door bedrijfsinformatie openbaar te maken via verschillende websites, zoals www.just4medical.nl en andere gelieerde domeinen.  \n", "   - Productie 64 bevat recente screenshots van de website www.just4medical.nl als bewijs van deze vermeende inbreuk.  \n", "\n", "2. **Algemene Voorwaarden van Just4Safety**:  \n", "   - De algemene voorwaarden van Just4Safety, versie december 2023, bevatten bepalingen die volgens Hooimeijer Holding B.V. bevestigen dat Just4Safety nog steeds diensten aanbiedt rondom coronasneltesten. Artikel 14.3 wordt hierbij specifiek genoemd.  \n", "   - Deze algemene voorwaarden worden gebruikt op meerdere gelieerde websites, wat de connectie tussen deze websites en Just4Safety benadrukt.  \n", "\n", "3. **Overige aanvullende producties**:  \n", "   - <PERSON><PERSON> uitgebre<PERSON> overzicht van producties wordt verstrekt, vari<PERSON><PERSON><PERSON> van correspondent<PERSON> (zoals e-mails en WhatsApp-berichten), uittreksels uit het Handelsregister, tot documenten die gerelateerd zijn aan eerdere overeenkomsten en financiële transacties. Deze dienen ter ondersteuning van de stellingen van Hooimeijer.  \n", "   - Enkele producties bevatten communicatie die mogelijk relevant is voor het vaststellen van de feiten in deze zaak, zoals e-mails tussen Hooimeijer en Van Wijk, facturen, en beslagstukken.\n", "\n", "4. **Juridische context**:  \n", "   - Het document verwijst naar relevante juridische principes, zoa<PERSON> de geheimhoudingsverplichting en de gevolgen van het schenden hiervan, inclusief boetes en schadevergoedingen.  \n", "   - Artikel 14.3 van de algemene voorwaarden van Just4Safety wordt aangehaald, waarin Just4Safety geen garantie biedt op de betrouwbaarhe<PERSON> van testresultaten en disclaimers hanteert.  \n", "   - Daarnaast worden bepalingen rondom eigendomsvoorbehoud, aansprakelijkheid, en geheimhouding uit de algemene voorwaarden van Just4Safety besproken.\n", "\n", "---\n", "\n", "#### **Relevante wetgeving**\n", "- **Wet normering buitengerechtelijke incassokosten**: Deze wet wordt genoemd in verband met de verplichtingen van de wederpartij bij verzuim.  \n", "- **Algemene Verordening Gegevensbescherming (AVG)**: Wordt aangehaald in relatie tot de verwerking van persoonsgegevens.  \n", "- **Geheimhoudingsovereenkomst**: Het document benadrukt de juridische gevolgen van het schenden van een geheimhoudingsplicht, inclusief boetes en mogelijke schadevergoeding.\n", "\n", "---\n", "\n", "#### **St<PERSON><PERSON><PERSON><PERSON> van de documenten**\n", "Het document is opgebouwd uit meerdere paragrafen waarin de aanvullende producties worden gepresenteerd en toegelicht. De aanvullende producties zijn genummerd en variëren van bewijsstukken (zoals screenshots van websites) tot juridische en zakelijke correspondentie. De algemene voorwaarden van Just4Safety worden uitgebreid behandeld in de laatste delen van het document. \n", "\n", "De documenten zijn gestructureerd om een volledig beeld te geven van de geschillen, waarbij bewijsstukken en juridische argumenten elkaar aanvullen. Het productieoverzicht en de algemene voorwaarden spelen een centrale rol in het onderbouwen van de claims van Hooimeijer Holding B.V.\n", "\n", "---\n", "\n", "#### **Inconsistenties**\n", "- Er lijken geen directe inconsistenties tussen de verschillende documenten te zijn. De aanvullende producties en algemene voorwaarden ondersteunen de kern van de argumenten, maar het is niet du<PERSON>jk of alle producties voldoende bewijs leveren voor de gestelde inbreuken.  \n", "- Het bewijs dat op meerdere websites bedrijfsinformatie is gepubliceerd, kan nader onderzocht worden om te bepalen of dit daadwerkelijk een schending van de geheimhoudingsovereenkomst vormt.\n", "\n", "---\n", "\n", "#### **Leesvolgorde**\n", "Om een volledig beeld van de zaak te krijgen, wordt de volgende leesvolgorde aanbevolen:\n", "1. **Productieoverzicht en aanvullende producties** (p.1-2): Dit geeft een overzicht van alle ingebrachte bewijsstukken en hun relevantie. Begin met Productie 64 en 65, a<PERSON><PERSON><PERSON> deze direct betrekking hebben op de kern van de <PERSON>aa<PERSON>.  \n", "2. **Algemene voorwaarden van Just4Safety** (p.3-6): Deze bevatten belangrijke bepalingen die relevant zijn voor het geschil, z<PERSON><PERSON> geh<PERSON>, aansprakelijkheid en eigendomsvoorbehoud.  \n", "3. **Correspondentie en andere bewijsstukken**: Ga vervolgens door de e-mails, WhatsApp-berichten en andere communicatie om de context en onderbouwing van de claims te begrijpen.\n", "\n", "---\n", "\n", "#### **Referenties naar het originele document**\n", "- <PERSON><PERSON><PERSON><PERSON> van inbreuk op de geheimhoudingsovereenkomst: Productie 64 (p.1-2).  \n", "- Artikel 14.3 van de algemene voorwaarden: p.3-5.  \n", "- Overzicht van aanvullende producties: p.1-2.  \n", "- Juridische bepalingen rondom geheimhouding en eigendomsvoorbehoud: p.5-6.  \n", "\n", "De<PERSON> samenvatting biedt een helder overzicht van de hoofdpunten en relevante juridische context, en geeft een gestructureerde aanpak om de documenten efficiënt te bestuderen."]}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "completion_type=CompletionType.MULTIPLE_DOCUMENTS_SUMMARY\n", "completion_options=CompletionOptions(completion_type=completion_type)\n", "\n", "t0 = time()\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "timeline_stream = document_service.get_completion_stream(input_text='', completion_options=completion_options)\n", "\n", "results = []\n", "async for chunk in timeline_stream:\n", "    results.append(chunk.content)"]}, {"cell_type": "markdown", "id": "e8007b79-9b0a-493b-9de1-9ac7dec94114", "metadata": {}, "source": ["### Streaming document timeline"]}, {"cell_type": "code", "execution_count": 4, "id": "e62c5fcd-2092-44da-ac76-233c4fc708ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating chunk took  0.0 minutes. \n", "\n", "content='Tijdlijn wordt gegenereerd. Dit kan enkele minuten duren.' is_final=False metadata={'id': 'run-3f794a9f-0206-4504-a63f-bf724c6b6c2b-0', 'organization_id': '46e06422-585a-4986-bf0e-f4ae9b84897e', 'project_id': '77344fb2-5676-49d4-8a9a-4d6454ee84cd', 'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0}\n", "\n", "\n", "\n", "\n", "Generating chunk took  0.3 minutes. \n", "\n", "content=\"| datum      | tijd   | beschrijving                                                                                                                                                                                      | type            | partijen                                        | relevantie                                                                                                         | opmerking                                                                 | document naam                                                               |   pagina |   paragraaf |\\n|:-----------|:-------|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|:----------------|:------------------------------------------------|:-------------------------------------------------------------------------------------------------------------------|:--------------------------------------------------------------------------|:----------------------------------------------------------------------------|---------:|------------:|\\n| 2021-10-18 |        | E-mail van de heer Abdellaoui van VWS aan de heer Hooimeijer wordt ingediend als productie in de zaak.                                                                                            | Correspondentie | ['Abdellaoui', 'Hooimeijer']                    | Relevante correspondentie die mogelijk bewijs bevat voor de zaak.                                                  |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           4 |\\n| 2022-10-13 |        | Printscreens van de website van Just4Safety worden ingediend als bewijs van vermeende schending van de geheimhoudingsovereenkomst.                                                                | Gebeurtenis     | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.'] | Ondersteunt de claim dat Just4Safety vertrouwelijke informatie openbaar heeft gemaakt.                             |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           3 |\\n| 2022-11-18 |        | Printscreens van de website www.just4carehospitality.nl worden ingediend als bewijs van vermeende schending van de geheimhoudingsovereenkomst.                                                    | Gebeurtenis     | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.'] | Ondersteunt de claim dat Just4Safety vertrouwelijke informatie openbaar heeft gemaakt.                             |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           3 |\\n| 2023-12-01 |        | Herziening van de algemene voorwaarden van Just4Safety waarin het aanbieden van coronasneltesten als dienst expliciet vermeld blijft.                                                             | Gebeurtenis     | ['Just4Safety B.V.', 'Van Wijk', 'De Bruin']    | De herziening toont aan dat Just4Safety mogelijk in strijd handelt met de geheimhoudingsovereenkomst.              | Artikel 14.3 van de algemene voorwaarden wordt specifiek genoemd.         | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        1 |           3 |\\n| 2023-12-01 |        | Algemene voorwaarden Just4 versie december 2023 worden opnieuw ingediend als productie.                                                                                                           | Gebeurtenis     | ['Just4Safety B.V.']                            | De voorwaarden bevatten mogelijk belangrijke bepalingen die relevant zijn voor de juridische aspecten van de zaak. |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           4 |\\n| 2023-12-01 |        | Printscreens van de website www.just4nnedical.nl worden ingediend als bewijs van vermeende schending van de geheimhoudingsovereenkomst.                                                           | Gebeurtenis     | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.'] | Ondersteunt de claim dat Just4Safety vertrouwelijke informatie openbaar heeft gemaakt.                             |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           4 |\\n| 2023-12-01 |        | Whatsapp berichten inzake ingediende testen worden ingediend als productie.                                                                                                                       | Correspondentie | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.'] | De berichten kunnen bewijsmateriaal bevatten voor de juridische claims.                                            |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           4 |\\n| 2023-12-01 |        | E-mail van mevrouw Sohilait inzake de facturen aan NexusLabor wordt ingediend als productie.                                                                                                      | Correspondentie | ['Sohilait', 'NexusLabor']                      | De e-mail kan bewijsmateriaal bevatten dat relevant is voor de zaak.                                               |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        2 |           4 |\\n| 2023-12-01 |        | Artikel 15.2 van de algemene voorwaarden van Just4Safety stelt dat overtreding van de geheimhoudingsverplichting leidt tot een boete van €5.000, plus €250 per dag dat de overtreding voortduurt. | Gebeurtenis     | ['Just4Safety B.V.', 'Wederpartij']             | Belangrijk voor de zaak gezien de geheimhoudingsclausule een kernpunt vormt in de juridische strijd.               | De boeteclausule kan van invloed zijn op de schadeclaims van Just4Safety. | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        5 |           7 |\\n| 2023-12-01 |        | Artikel 17.2 van de algemene voorwaarden bevestigt dat alle geschillen worden beslecht door de rechtbank Gelderland, zittingsplaats Arnhem.                                                       | Gebeurtenis     | ['Just4Safety B.V.', 'Wederpartij']             | Bepaalt de bevoegde rechtbank voor geschillen tussen Just4Safety en de wederpartij.                                |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        6 |           2 |\\n| 2024-03-11 |        | Mondelinge behandeling bij de Rechtbank Gelderland in de zaak tussen Hooimeijer Holding B.V. en Just4Safety B.V.                                                                                  | Rechtszitting   | ['Hooimeijer Holding B.V.', 'Just4Safety B.V.'] | Belangrijke zitting waar aanvullende producties worden besproken en de zaak inhoudelijk wordt behandeld.           |                                                                           | Zitting_-_240227_Akte_aanvullende_producties_J4S_(met_goede_bladwijzer).pdf |        1 |           1 |\" is_final=True metadata={'id': 'run-0cc6dd46-fcb7-4723-a890-d0effa9ce3b7-0', 'organization_id': '46e06422-585a-4986-bf0e-f4ae9b84897e', 'project_id': '77344fb2-5676-49d4-8a9a-4d6454ee84cd', 'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0}\n", "\n", "\n", "\n", "\n"]}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "completion_type=CompletionType.DOCUMENT_SUMMARY\n", "completion_options=CompletionOptions(completion_type=CompletionType.TIMELINE)\n", "\n", "t0 = time()\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "timeline_stream = document_service.get_completion_stream(input_text='', completion_options=completion_options)\n", "\n", "async for chunk in timeline_stream:\n", "    \n", "    print(f\"Generating chunk took {(time() - t0) / 60: .1f} minutes. \\n\")\n", "    print(chunk)\n", "    print(\"\\n\\n\\n\")\n"]}, {"cell_type": "markdown", "id": "d5b7096c-220a-43bf-b9dd-4aeb2e84e342", "metadata": {}, "source": ["### Streaming project timeline"]}, {"cell_type": "code", "execution_count": 5, "id": "ec294fe1-5a4e-4f55-8ece-c794b3f39a0d", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating chunk took  0.0 minutes. \n", "\n", "content='Tijdlijn wordt gegenereerd. Dit kan enkele minuten duren.' is_final=False metadata={'id': 'run-ce96ec06-a344-4d38-b72c-e6b0f02dace1-0', 'organization_id': '46e06422-585a-4986-bf0e-f4ae9b84897e', 'project_id': '77344fb2-5676-49d4-8a9a-4d6454ee84cd', 'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0}\n", "\n", "\n", "\n", "\n", "Generating chunk took  0.5 minutes. \n", "\n", "content=\"| datum      | tijd   | beschrijving                                                                                                                                             | type                   | partijen                                               | relevantie                                                                                                         | opmerking                                                    | document naam                                     |   pagina |   paragraaf |\\n|:-----------|:-------|:---------------------------------------------------------------------------------------------------------------------------------------------------------|:-----------------------|:-------------------------------------------------------|:-------------------------------------------------------------------------------------------------------------------|:-------------------------------------------------------------|:--------------------------------------------------|---------:|------------:|\\n| 2020-10-16 |        | Van Wijk en Hooimeijer spraken over de gezamenlijke exploitatie van teststraten onder de naam Coronasneltestdirect.                                      | Gebeurtenis            | ['Van Wijk', 'Hooimeijer']                             | Dit markeert het begin van de samenwerking tussen de betrokken partijen.                                           | Dit vond plaats na de tweede uitbraak van de coronapandemie. | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\\n| 2021-02-27 |        | Hooimeijer sloot een overeenkomst genaamd Antigeen Sneltesten Covid-19 met NexusLabor B.V.                                                               | Juridische Stap        | ['Hooimeijer', 'NexusLabor B.V.']                      | Deze overeenkomst was essentieel voor het verkrijgen van subsidies voor afgenomen coronatesten.                    |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           1 |\\n| 2021-03-09 |        | HIC sloot een overeenkomst met NexusLabor B.V. voor de exploitatie van coronateststraten.                                                                | Juridische Stap        | ['Health Improvement Company B.V.', 'NexusLabor B.V.'] | HIC trad toe tot de markt voor coronateststraten en sloot een overeenkomst vergelijkbaar met die van Hooimeijer.   |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           3 |\\n| 2021-05-26 |        | Hooimeijer sloot een overeenkomst met ArboVita B.V. na ontevredenheid over samenwerking met NexusLabor.                                                  | Juridische Stap        | ['Hooimeijer', 'ArboVita B.V.']                        | Deze nieuwe overeenkomst verving die met NexusLabor en markeerde een wijziging in Hooimeijer's zakelijke relaties. |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\\n| 2021-05-30 |        | Hooimeijer beëindigde de overeenkomst met NexusLabor B.V.                                                                                                | Juridische Stap        | ['Hooimeijer', 'NexusLabor B.V.']                      | Dit markeerde het einde van de samenwerking tussen Hooimeijer en NexusLabor.                                       |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\\n| 2021-06-30 |        | Just4Safety trad in de plaats van de VOF met betrekking tot de samenwerking met Hooimeijer.                                                              | Gebeurtenis            | ['Just4Safety', 'Hooimeijer']                          | Dit markeerde een belangrijke overgang in de juridische entiteit van de samenwerking.                              | De VOF werd opgeheven en vervangen door Just4Safety.         | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           4 |\\n| 2021-08-26 |        | Hooimeijer legde conservatoir derdenbeslag ten laste van NexusLabor vanwege niet-betaalde facturen.                                                      | Juridische Stap        | ['Hooimeijer', 'NexusLabor B.V.']                      | Het beslag was een poging van Hooimeijer om betaling te verkrijgen voor niet-betaalde facturen.                    |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\\n| 2022-03-01 |        | De samenwerking tussen Just4Safety en Hooimeijer eindigde.                                                                                               | Gebeurtenis            | ['Just4Safety', 'Hooimeijer']                          | Dit markeert het einde van de samenwerking tussen beide partijen in Coronasneltestdirect.                          |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\\n| 2022-11-17 |        | Tijdens een zitting in een kort geding tussen Hooimeijer en NexusLabor werd een schikking getroffen waarbij NexusLabor €167.000 aan Hooimeijer betaalde. | Rechtszitting          | ['Hooimeijer', 'NexusLabor B.V.']                      | De schikking loste het geschil over de conservatoire beslaglegging op.                                             |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           5 |\\n| 2022-11-18 |        | Hooimeijer maakte aanspraak op een boete van €100.000 uit de geheimhoudingsovereenkomst en een schadevergoeding van €113.278,50 van Just4Safety.         | Correspondentie        | ['Hooimeijer', 'Van Wijk', 'De Bruin', 'Just4Safety']  | Dit markeert een nieuwe fase van financiële en juridische claims tussen de partijen.                               |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           6 |\\n| 2022-11-24 |        | Just4Safety wees de vorderingen van Hooimeijer van de hand en sommeerde Hooimeijer om uiterlijk 2 december 2022 te voldoen aan een eerdere sommatie.     | Correspondentie        | ['Just4Safety', 'Hooimeijer']                          | Dit markeert een verdere escalatie in het conflict tussen de partijen.                                             |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           7 |\\n| 2023-08-08 |        | Hooimeijer legde conservatoir derdenbeslag ten laste van Just4Safety, Van Wijk en De Bruin.                                                              | Juridische Stap        | ['Hooimeijer', 'Just4Safety', 'Van Wijk', 'De Bruin']  | Dit was een poging van Hooimeijer om haar claims te waarborgen.                                                    |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        2 |           7 |\\n| 2023-09-06 |        | Het tussenvonnis werd uitgesproken in de zaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                         | Rechterlijke Uitspraak | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']        | Dit markeert een belangrijke stap in de juridische procedure tussen beide partijen.                                |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\\n| 2024-03-11 |        | Een mondelinge behandeling vond plaats in de zaak tussen Just4Safety B.V. en Hooimeijer Holding B.V.                                                     | Rechtszitting          | ['Just4Safety B.V.', 'Hooimeijer Holding B.V.']        | Dit was een cruciale gelegenheid voor beide partijen om hun standpunten mondeling toe te lichten.                  |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        1 |           1 |\\n| 2024-06-05 |        | De rechtbank veroordeelt Hooimeijer om aan Just4Safety €63.543,84 te betalen, vermeerderd met wettelijke rente vanaf 2 december 2022.                    | Rechterlijke Uitspraak | ['Hooimeijer', 'Just4Safety']                          | Dit is een belangrijke uitspraak in het geschil tussen beide partijen.                                             |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           6 |\\n| 2024-06-05 |        | De rechtbank veroordeelt Hooimeijer in de proceskosten van Just4Safety, begroot op €3.858,00.                                                            | Rechterlijke Uitspraak | ['Hooimeijer', 'Just4Safety']                          | Dit geeft financiële gevolgen voor Hooimeijer in het geschil.                                                      |                                                              | 240605_van_Rechtbank_Gelderland_vonnis_J4S_HH.pdf |        8 |           7 |\" is_final=True metadata={'id': 'run-b6d0e356-6969-482a-a0e7-fd3d00e17bd1-0', 'organization_id': '46e06422-585a-4986-bf0e-f4ae9b84897e', 'project_id': '77344fb2-5676-49d4-8a9a-4d6454ee84cd', 'prompt_tokens': 0, 'completion_tokens': 0, 'total_tokens': 0}\n", "\n", "\n", "\n", "\n"]}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "completion_type=CompletionType.DOCUMENT_SUMMARY\n", "completion_options=CompletionOptions(completion_type=CompletionType.TIMELINE)\n", "\n", "t0 = time()\n", "project_service = ProjectCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "timeline_stream = project_service.get_completion_stream(input_text='', completion_options=completion_options)\n", "\n", "async for chunk in timeline_stream:\n", "    \n", "    print(f\"Generating chunk took {(time() - t0) / 60: .1f} minutes. \\n\")\n", "    print(chunk)\n", "    print(\"\\n\\n\\n\")\n"]}, {"cell_type": "markdown", "id": "1c4bceeb-7251-4a2a-a1f9-cf98bdda318f", "metadata": {}, "source": ["### Streaming Project Summary"]}, {"cell_type": "code", "execution_count": 9, "id": "cdf83d27-30e0-4d3c-a815-fde6cb1f4d2d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["### Gedetailleerde en uitgebreide samenvatting van de juridische documenten\n", "\n", "#### **<PERSON><PERSON><PERSON><PERSON> van de juridische documenten**\n", "Dit is een samenvatting van de verstrekte juridische documenten die zijn opgenomen in dit project. De samenvatting behandelt de acht<PERSON>grond, kernproblemen van het geschil, vorderingen, juridische overeenkomsten, tegenvorderingen, eerdere pogingen tot geschillenbeslechting, verzoeken aan de rechtbank en een conclusie.\n", "\n", "---\n", "\n", "#### **Context en Achtergrond**\n", "De zaak betreft een geschil tussen Just4Safety B.V. (eisende partij in conventie en verwerende partij in reconventie) en Hooimeijer Holding B.V. (gedaagde partij in conventie en eisende partij in reconventie). \n", "\n", "- **Relaties en voorgeschiedenis**: Op 16 oktober 2020 zijn <PERSON><PERSON> en Hooimeijer in gesprek gegaan over een samenwerking onder de naam \"Coronasneltestdirect\", waarbij werkne<PERSON> van bedrijven of instellingen getest konden worden op Covid-19. <PERSON> Wijk en L. de Bruin waren destijds vennoten van Just4Safety V.O.F. en actief in medische zorghulpverlening. Voorafgaand aan de samenwerking werd een geheimhoudingsovereenkomst ondertekend door Van Wijk en De Bruin, waarin Hooimeijer werd aangeduid als de \"Verstrekkende Partij\". De samenwerking werd formeel aangegaan, waarbij de VOF verantwo<PERSON>elijk was voor het afnemen van de coronatesten, en Hooimeijer verantwoordelijk was voor administratie, financiën en contracten.\n", "\n", "- **Exploitatie en structuur**: Er werden vier teststraten opgezet in Nederland. De opbrengsten van Coronasneltestdirect werden na aftrek van operationele kosten gelijk verdeeld (50-50) tussen de VOF en Hooimeijer. Subsidie voor de tests werd verkregen via het <PERSON><PERSON> van Volksgezondheid, Welzijn en Sport, met tuss<PERSON><PERSON><PERSON> van een bedrij<PERSON>s of arbodienst. Hooimeijer sloot overeenkomsten met NexusLabor B.V. en later met ArboVita B.V. voor de uitvoering van de testen en de subsidieaanvragen.\n", "\n", "- **Overgang naar Just4Safety B.V.**: Vanaf 30 juni 2021 werd Just4Safety B.V. opgericht en nam deze de activa en passiva van de VOF over. De samenwerking tussen Just4Safety en Hooimeijer eindigde op 1 maart 2022.\n", "\n", "---\n", "\n", "#### **Kernproblemen van het geschil**\n", "Het geschil draait om de financiële afwikkeling van de samenwerking tussen Just4Safety en Hooimeijer. Belangrijke punten van discussie zijn:\n", "\n", "1. **Beta<PERSON> van facturen**: Hooimeijer heeft facturen ter waarde van € 196.950,-- voor uitgevoerde coronatesten over mei-juli 2021 naar NexusLabor gestuurd, die niet werden betaald. Dit leidde tot conservatoir derdenbeslag door Hooimeijer en een schikking met NexusLabor voor € 167.000,--.\n", "2. **Geheimhoudingsovereenkomst**: Hooimeijer stelde dat Just4Safety en haar bestuurders de geheimhoudingsovereenkomst hebben geschonden en vorderde een boete van € 100.000,--.\n", "3. **Verdeling van subsidies**: Just4Safety stelde dat Hooimeijer een bedrag van € 171.346,50 had kunnen claimen bij NexusLabor, wa<PERSON><PERSON> Just4Safety de helft eiste (€ 85.673,25).\n", "4. **Onverschuldigde betaling en schadevergoeding**: Hooimeijer vorderde een bedrag van € 113.278,50 als voorlopige schadevergoeding en € 20.000,-- wegens onverschuldigde betaling.\n", "\n", "---\n", "\n", "#### **Vorderingen en Eisen**\n", "**Just4Safety B.V.:**\n", "- <PERSON><PERSON><PERSON> betaling van € 85.673,25, zi<PERSON><PERSON> de helft van het bedrag dat Ho<PERSON>mei<PERSON> had kunnen claimen bij NexusLabor.\n", "- Wij<PERSON> de vorderingen van Ho<PERSON>meijer (boete uit geheimhoudingsovereenkomst en schadevergoeding) van de hand.\n", "\n", "**Hooimeijer Holding B.V.:**\n", "- <PERSON><PERSON><PERSON> een boete van € 100.000,-- we<PERSON> vermeende schending van de geheimhoudingsovereenkomst.\n", "- Eist € 113.278,50 als voorlopige schadevergoeding en € 20.000,-- wegens onverschuldigde betaling.\n", "\n", "---\n", "\n", "#### **Juridische overeenkomsten en contracten**\n", "1. **Geheimhoudingsovereenkomst**: <PERSON><PERSON> overeenko<PERSON>t verplichtte Van Wijk en De Bruin tot strikte geheimhouding van informatie over het ondernemingsplan van Hooimeijer. <PERSON><PERSON><PERSON> schen<PERSON> was een boete van € 100.000,-- ve<PERSON><PERSON>igd.\n", "2. **<PERSON><PERSON><PERSON><PERSON><PERSON> met NexusLabor**: Hooimeijer sloot een over<PERSON><PERSON><PERSON>t met NexusLabor voor het verkrijgen van subsidies voor coronatesten.\n", "3. **Samenwerkingsovereenkomst**: De samenwerking tussen de VOF (later Just4Safety) en Hooimeijer was gebaseerd op een gelijkwaardige verdeling van de opbrengsten.\n", "\n", "---\n", "\n", "#### **<PERSON><PERSON><PERSON><PERSON> <PERSON>partij**\n", "Hooimeijer heeft de volgende tegenclaims ingediend:\n", "- Boete van € 100.000,-- we<PERSON> vermeende schending van de geheimhoudingsovereenkomst.\n", "- Schadevergoeding van € 113.278,50.\n", "- Terugbetaling van € 20.000,-- wegens onverschuldigde betaling.\n", "\n", "---\n", "\n", "#### **Eerdere pogingen tot geschillenbeslechting**\n", "Er was een schikking tussen Hooimeijer en NexusLabor in een kort geding bij de rechtbank Oost-Brabant, waarbij NexusLabor een bedrag van € 167.000,-- betaalde aan <PERSON>. Just4Safety eiste nadien dat Hooimeijer de helft van dit bedrag aan haar zou betalen. Pogingen tot betaling of overeenstemming tussen Just4Safety en Hooimeijer liepen op niets uit.\n", "\n", "---\n", "\n", "#### **Procedurele verzoeken aan de rechtbank**\n", "- Conservatoir derdenbeslag gelegd door Hooimeijer tegen Just4Safety, <PERSON> en De Bruin.\n", "- Just4Safety vorderde betaling van de he<PERSON>t van het bedrag dat <PERSON> had ontvangen van NexusLabor.\n", "- Hooimeijer vorderde meerdere bedragen (boete, schadevergoeding en onverschuldigde betaling).\n", "\n", "---\n", "\n", "#### **Con<PERSON>lusie**\n", "De rechtbank heeft het volgende beslist:\n", "1. **In conventie**:\n", "   - Hooimeijer wordt veroordeeld om € 63.543,84 aan Just4Safety te betalen, verm<PERSON>derd met wettelijke rente vanaf 2 december 2022.\n", "   - Hooimeijer moet de proceskosten van Just4Safety betalen (€ 8.271,73).\n", "\n", "2. **In reconventie**:\n", "   - De vorderingen van Hooimeijer tegen Just4Safety worden afgewezen.\n", "   - Hooimeijer moet de proceskosten van Just4Safety betalen (€ 3.858,00).\n", "\n", "De rechtbank verklaarde dat Just4Safety de geheimhoudingsovereenkomst niet heeft geschonden en wees de boete van € 100.000,-- af. Daarnaast werden de overige vorderingen van Hooimeijer (schadevergoeding en onverschuldigde betaling) eveneens afgewezen."]}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "completion_type=CompletionType.PROJECT_SUMMARY\n", "completion_options=CompletionOptions(completion_type=completion_type)\n", "\n", "t0 = time()\n", "project_service = ProjectCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "completion_stream = project_service.get_completion_stream(input_text='', completion_options=completion_options)\n", "\n", "results = []\n", "async for chunk in completion_stream:\n", "    results.append(chunk.content)"]}, {"cell_type": "markdown", "id": "017b4cc0-8cbe-4815-8659-43f5468c6a10", "metadata": {}, "source": ["# Test Summary"]}, {"cell_type": "markdown", "id": "027df23f-ca5a-4eeb-ad59-19a9fc745a77", "metadata": {}, "source": ["### Single Document"]}, {"cell_type": "code", "execution_count": 2, "id": "21a7534e-e1c6-4055-83e2-5ee015fc5856", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Container for project 77344fb2-5676-49d4-8a9a-4d6454ee84cd in organization 46e06422-585a-4986-bf0e-f4ae9b84897e not found. Falling back to using the retriever\n"]}, {"data": {"text/markdown": ["### <PERSON><PERSON><PERSON><PERSON> van het document\n", "\n", "#### **Hoofdgedachte**\n", "Het document betreft een akte van overlegging van aanvullende producties door Hooimeijer Holding B.V. in een juridische procedure tegen Just4Safety B.V. Het centrale thema is de stelling van Hooimeijer dat Just4Safety, samen met betrok<PERSON><PERSON> Van Wijk en De Bruin, inbreuk maakt op een geheimhoudingsovereenkomst. Deze inbreuken zouden blijken uit het exploiteren van Hooimeijers ondernemingsplan via meerdere websites en uit specifieke bepalingen in de algemene voorwaarden van Just4Safety.\n", "\n", "---\n", "\n", "#### **Belangrijkste punten**\n", "1. **Over<PERSON><PERSON> van de geheimhoudingsovereenkomst**  \n", "   - Just4Safety en de betrokken partijen zouden vertrouwelijke informatie, waaronder het ondernemingsplan van Hooimeijer, he<PERSON><PERSON> geëxploiteerd via verschillende websites, waaronder www.just4medical.nl. (Zie productie 64).\n", "   - Nieuwe printscreens van de website www.just4medical.nl worden ingebracht als bewijs van de voortdurende inbreuk.\n", "\n", "2. **Algemene voorwaarden van Just4Safety**  \n", "   - Uit de meest recente versie van de algemene voorwaarden van Just4Safety (december 2023) blijkt dat zij nog steeds diensten aanbieden die verband houden met coronasneltesten, wat volgens Hooimeijer ook een schending van de geheimhoudingsovereenkomst inhoudt. Dit wordt ondersteund door artikel 14.3 van de algemene voorwaarden (zie productie 65).\n", "\n", "3. **Overzicht van ingebrachte producties**  \n", "   - Het document bevat een uitgebreid productieoverzicht (producties 1-65), wa<PERSON>n bewijsstukken worden gepresenteerd die variëren van correspondentie en overeenkomsten tot printscreens en financiële gegevens. Deze producties ondersteunen Hooimeijers beweringen over de schending van de geheimhoudingsovereenkomst.\n", "\n", "4. **Relevante juridische principes**  \n", "   - De geheimhoudingsovereenkomst tussen de partijen vormt de kern van het geschil. Het document benadrukt de verplichting tot vertrouwelijkheid en de gevolgen van schen<PERSON>, zoals schadeclaims en mogelijke boetes.\n", "   - Artikel 14.3 van de algemene voorwaarden van Just4Safety wordt aangehaald om aan te tonen dat Just4 haar verplichtingen jegens Hooimeijer niet naleeft.\n", "\n", "---\n", "\n", "#### **St<PERSON><PERSON><PERSON><PERSON> van de documenten**\n", "De documenten zijn gestructureerd als volgt:\n", "- **Akte van overlegging**: De akte bevat de introductie en toelichting op de aanvullende producties.\n", "- **Productieoverzicht**: <PERSON><PERSON> lij<PERSON> van alle ingebrachte producties (1-65), die dienen als bewijsstukken voor de claims van Hooimeijer.\n", "- **Uitwerking producties 64 en 65**: Details over de schendingen via de website www.just4medical.nl en de algemene voorwaarden van Just4Safety.\n", "- **Algemene voorwaarden (bijlage)**: <PERSON><PERSON> volledige tekst van de algemene voorwaarden van Just4Safety, die relevant zijn voor de beoordeling van de <PERSON>aa<PERSON>.\n", "\n", "---\n", "\n", "#### **Inconsistenties**\n", "Er worden in het document geen expliciete inconsistenties of tegenstrijdigheden genoemd. De aangevoerde producties lijken een samenhangend betoog te vormen, waarin bewijsstuk<PERSON> el<PERSON>ar onders<PERSON>unen.\n", "\n", "---\n", "\n", "#### **Aanbevolen leesvolgorde**\n", "1. **A<PERSON><PERSON> van overlegging (pagina 1)**: Dit geeft een overzicht van het doel van de aanvullende producties en de centrale claims.\n", "2. **<PERSON><PERSON> 64**: Printscreens van www.just4medical.nl, die de inbreuk op de geheimhoudingsovereenkomst aantonen.\n", "3. **<PERSON><PERSON> 65**: De algemene voorwaarden van Just4Safety, die verdere ondersteuning bieden voor de schendingen.\n", "4. **Productieoverzicht**: Voor een volledig beeld van de overige bewijsmaterialen.\n", "5. **Algemene voorwaarden (volledige tekst)**: Voor een gedetailleerde juridische analyse.\n", "\n", "---\n", "\n", "### Con<PERSON><PERSON>ie\n", "Het document biedt een uitgebreide onderbouwing van de claims van Hooimeijer tegen Just4Safety, met een focus op schendingen van een geheimhoudingsovereenkomst en de rol van de algemene voorwaarden van Just4Safety. Het bewijs wordt gestructureerd gepresenteerd via een akte en een productieoverzicht, waarbij de producties 64 en 65 de kern vormen van het argument."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await document_service.get_completion(input_text='', completion_type=CompletionType.DOCUMENT_SUMMARY)\n", "display(Markdown(foo.content))"]}, {"cell_type": "markdown", "id": "9248d692-ea5f-444a-b003-34346f90e998", "metadata": {}, "source": ["### Multiple Documents"]}, {"cell_type": "code", "execution_count": 3, "id": "d85f763a-0f1c-451a-a81c-7d6fcdd16ef6", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Container for project 77344fb2-5676-49d4-8a9a-4d6454ee84cd in organization 46e06422-585a-4986-bf0e-f4ae9b84897e not found. Falling back to using the retriever\n"]}, {"data": {"text/markdown": ["### <PERSON><PERSON><PERSON><PERSON>\n", "\n", "#### Ho<PERSON><PERSON><PERSON><PERSON>\n", "Het centrale thema van de documenten betreft het juridische geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V., waari<PERSON> stelt dat Just4Safety meerdere contractuele verplichtingen heeft geschonden, waaronder schending van een geheimhoudingsovereenkomst en onrechtmatig handelen met betrekking tot het aanbieden van coronasneltesten. Daarnaast worden diverse aanvullende producties ingebracht om de vorderingen van Hooimeijer te onderbouwen, waaronder bewijsstukken voor beslagleggingen, contracten en communicatie tussen partijen.\n", "\n", "---\n", "\n", "#### Belangrijkste punten\n", "\n", "1. **<PERSON><PERSON><PERSON> <PERSON>hou<PERSON>sovereenko<PERSON>t:**\n", "   - Hooimeijer beschuldigt Just4Safety van het delen en exploiteren van vertrouwelijke informatie, zoals het ondernemingsplan, op verschillende websites, waaronder just4medical.nl (Productie 64). Dit zou een directe schending van de geheimhoudingsovereenkomst inhouden.\n", "   - Artikel 14.3 van de algemene voorwaarden van Just4Safety (versie december 2023) wordt genoemd als bewijs dat Just4 nog steeds coronasneltesten aanbiedt, wat in strijd z<PERSON> zijn met e<PERSON><PERSON> afspra<PERSON> (Productie 65).\n", "\n", "2. **Conservatoire beslagleggingen:**\n", "   - Hooimeijer heeft conservatoir beslag laten leggen op de tegoeden van Just4Safety bij verschillende banken, waaronder ABN Amro, Rabobank, ING en de Volksbank (Producties 59-60). Deze beslagleggingen dienen ter zekerheidstelling van een vordering van € 246.507,92.\n", "   - De derdenverklaringen van de banken tonen aan dat er slechts beperkte tegoeden beschikbaar zijn (bijvoorbeeld € 6.712,81 bij Rabobank na verrekening).\n", "\n", "3. **Onderbouwing van aansprakelijkheid:**\n", "   - Producties zoals e-mails, Whatsapp-berichten en overeenkomsten (bijv. Producties 61-63) worden gebruikt om aan te tonen dat Just4Safety verantwoordelijk was voor het indienen van subsidieaanvragen en facturatie, wat relevant is voor de schadeclaims.\n", "\n", "4. **Juridische context:**\n", "   - Het geschil wordt behandeld door de rechtbank Gelderland, locatie Arnhem, onder zaaknummer C/05/421257.\n", "   - De beslagleggingen zijn uitgevoerd op basis van artikel 718 Rv (Wetboek van Burgerlijke Rechtsvordering), en Just4Safety wordt verplicht om gegevens over haar tegoeden te verstrekken.\n", "\n", "---\n", "\n", "#### <PERSON><PERSON><PERSON><PERSON><PERSON> van de documenten\n", "\n", "1. **Documenten en producties:**\n", "   - De documenten bestaan voornamelijk uit processtukken zoals aktes van overlegging van aanvullende producties, beslagstukken, algemene voorwaarden, communicatie tussen partijen, en verklaringen van derden.\n", "   - Elk document bevat een overzicht van de ingebrachte producties en een toelichting op de relevantie ervan in de context van het geschil.\n", "\n", "2. **B<PERSON><PERSON><PERSON><PERSON> aan het hoofdthema:**\n", "   - De aktes van overlegging (bijv. Zitting_-_240227 en Zitting_-_231219) structureren de ingebrachte bewijsmaterialen en koppelen deze aan specifieke juridische claims.\n", "   - Beslagstukken (Producties 59-60) onderbouwen de financiële vorderingen van Hooimeijer.\n", "   - Algemen<PERSON> (Productie 65) en printscreens van websites (Productie 64) ondersteunen de beschuldigingen van schending van de geheimhoudingsovereenkomst.\n", "\n", "---\n", "\n", "#### Inconsistenties\n", "\n", "Er lijken geen directe tegenstrijdigheden binnen de documenten te zijn. Wel is er een mogelijk verschil in interpretatie tussen partijen over de verantwoordelijkheden met betrekking tot het indienen van subsidies (Productie 62). Dit verschil kan van invloed zijn op de beoordeling van aansprakelijkheid.\n", "\n", "---\n", "\n", "#### Aanbevolen leesvolgorde\n", "\n", "1. **Zitting_-_240227_Akte_aanvullende_producties_J4S:**\n", "   - Dit document bevat de meest recente producties en legt de nadruk op de kernclaims, zoa<PERSON> de schending van de geheimhoudingsovereenkomst.\n", "2. **Zitting_-_231219_Akte_houdende_overlegging_aanvullende_producties_J4S:**\n", "   - Dit document biedt aanvullende context over de beslagleggingen en financiële onderbouwing.\n", "3. **Producties 64 en 65:**\n", "   - <PERSON><PERSON> zijn direct relevant voor de beschuldigingen met betrekking tot de schending van de geheimhoudingsovereenkomst.\n", "4. **Beslagstukken en derdenverklaringen (Producties 59-60):**\n", "   - <PERSON><PERSON> onderbouwen de financiële vorderingen en de status van het beslag.\n", "\n", "---\n", "\n", "#### Con<PERSON><PERSON>ie\n", "\n", "De documenten bieden een gestructureerd overzicht van de juridische claims en bewijsmaterialen in het geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V. De nadruk ligt op schendingen van de geheimhoudingsovereenkomst, de financiële gevo<PERSON>, en de juridische acties die Hooimeijer heeft ondernomen. Het bewijsmateriaal lijkt grotendeels consistent en ondersteunt de kernclaims van Hooimeijer."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from uuid import UUID\n", "organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "document_service = DocumentCompletionService(cosmodb_client=cosmosdb, retriever_service=cosmosdb._retriever)\n", "foo = await document_service.get_completion(input_text='', completion_type=CompletionType.DOCUMENT_SUMMARY)\n", "display(Markdown(foo.content))"]}, {"cell_type": "markdown", "id": "0c2337dd-1b05-400b-a5ca-e70fc47e59ff", "metadata": {}, "source": ["# Search Case Law"]}, {"cell_type": "markdown", "id": "a4909253-e67b-4194-9b83-d17a4380507d", "metadata": {}, "source": ["### Return IDs only"]}, {"cell_type": "code", "execution_count": 3, "id": "69fec52b-01d7-4695-a0f9-8a01d303c242", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\OneDrive - Metyis\\Documenten\\Personal Documents\\LegalPA\\statuta-rag-api-v2\\src\\app\\services\\rechtspraak_search_service.py:84: LangChainDeprecationWarning: The method `BaseChatModel.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  response = self._chat_client([\n"]}, {"data": {"text/plain": ["['ECLI:NL:GHAMS:2023:1485',\n", " 'ECLI:NL:RBZWO:2004:AR2772',\n", " 'ECLI:NL:RBNHO:2024:11205',\n", " 'ECLI:NL:GHSHE:2023:3785',\n", " 'ECLI:NL:GHARN:2009:BJ0914',\n", " 'ECLI:NL:RBNNE:2024:1576',\n", " 'ECLI:NL:RBDHA:2022:13604',\n", " 'ECLI:NL:RBAMS:2012:BW2924',\n", " 'ECLI:NL:RBARN:2010:BO2136',\n", " 'ECLI:NL:RBMNE:2014:6750']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["query=\"<PERSON><PERSON><PERSON> go<PERSON>e onverwacht de deur van zijn bestelbusje open waarna client er op volle snelheid tegenaan is gefietst, waardoor hij is gevallen en een hersenschudding en een gebroken arm heeft.\"\n", "rechtspraak_service = RechtspraakSearchService()\n", "relevant_legal_cases = await rechtspraak_service.search(query=query, return_ids_only=True)\n", "relevant_legal_cases"]}, {"cell_type": "markdown", "id": "3b961d1c-a581-4c66-b117-da47dfb0640f", "metadata": {}, "source": ["### Full text of cases"]}, {"cell_type": "code", "execution_count": 4, "id": "c80206a7-83b0-4605-85d8-0336c3a16927", "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Unclosed client session\n", "client_session: <aiohttp.client.ClientSession object at 0x00000278D2835460>\n", "Unclosed connector\n", "connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x00000278D2908230>, 47722.031)]']\n", "connector: <aiohttp.connector.TCPConnector object at 0x00000278D2837A70>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Enchanced query used for semantic search: kleinschalige wietplantage, 10 planten, geen groot<PERSON>el, geen energiediefstal, strafrechtelijke vervolging, he<PERSON><PERSON><PERSON><PERSON>, gering<PERSON> ho<PERSON>, persoonlijke gebruik, stra<PERSON><PERSON><PERSON>, rechterlijke uitspraken\n", "\n", "Also consider the following 10 relevant legal cases:\n", "Case: ECLI:NL:GHAMS:2020:2419, Gerechtshof Amsterdam, 08-09-2020, 23-004205-18.\n", "Case Summary: Medeple<PERSON> (344 planten), vrijspraak diefstal elektriciteit..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNNE:2021:5376, Rechtbank Noord-Nederland, 21-12-2021, 18/117972-21.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON><PERSON>, grote ho<PERSON>heid planten. \n", "Gelet op de wijze waaro<PERSON> de hennepkwekerij was opgezet en het aantal aanwezige hennepplanten was sprake van een zekere mate van  professionaliteit met betrekking tot de hennepkwekerij. Verdachte heeft bijgedragen aan het in stand houden van het illegale hennepcircuit. Door de productie van hennepplanten wordt de volksgezondheid in gevaar gebracht en verd....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBROT:2018:4754, Rechtbank Rotterdam, 09-05-2018, 10/038099-18 / Raadkamernummer 18/762.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> he<PERSON> geteeld voor eigen medicinaal gebruik. 552a Sv-beklag niet ontvankelijk. Ook kleinschalige teelt is strafbaar..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBROT:2014:6456, Rechtbank Rotterdam, 25-07-2014, KTN-2498346.\n", "Case Summary: <PERSON><PERSON><PERSON> vraag of er sprake is van hennepteelt voor eigen gebruik dan wel van beroep<PERSON>- of bedrijfsmatige hennepteelt is van belang hoeveel planten (bij gedaagde zijn 3 à 4 planten aangetroffen) maar ook welke apparatuur in de betreffende ruimte is aangetroffen.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3039, Rechtbank Midden-Nederland, 28-07-2022, 16/707304-17 (P).\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> heeft in de periode van 1 maart 2015 tot en met 9 oktober 2018 als groothandel een grote hoeveelheid voorwerpen en stoffen, die bestemd zijn voor de illegale hennept<PERSON>t, voor<PERSON><PERSON> gehad, te koop a<PERSON>, verk<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, verstrekt en vervoerd. Hiermee heeft verdachte zich ruim drie en een half jaar schuldig gemaakt aan overtreding van artikel 11a van de Opiumwet. Daarnaast h....\n", "Full text: \n", "\n", "Case: ECLI:NL:GHDHA:2022:1475, Gerechtshof Den Haag, 03-08-2022, **********.\n", "Case Summary: Strafrechtelijke vervolging voor hennepteelt in woning en diefstal stroom, terwijl eerder een bestuurlijke boete was opgelegd voor het zonder vergunning onttrekken van woonruimte aan de woningvoorraad, ten behoe<PERSON> van hennepteelt. \n", "Het hof is van oordeel dat geen sprake is van ‘hetzelfde feit’ als bedoeld in art. 68 Sr, reeds gelet op de juridische aard van de feiten.\n", "Officier van justiti<PERSON> is o....\n", "Full text: \n", "\n", "Case: ECLI:NL:GHAMS:2015:3786, Gerechtshof Amsterdam, 20-07-2015, 23-002034-14.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON> ho<PERSON><PERSON><PERSON><PERSON> he<PERSON><PERSON><PERSON><PERSON><PERSON>.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3055, Rechtbank Midden-Nederland, 28-07-2022, 16/706555-18 (P).\n", "Case Summary: Verd<PERSON><PERSON> is vrijgesproken van het ten laste gelegde nu niet kan worden bewezen dat hij feitelijk leiding heeft gegeven aan een groothandel die in de periode van 1 maart 2015 tot en met 9 oktober 2018 als groothandel een grote hoeveelheid voorwerpen en stoffen, die bestemd zijn voor de illegale hennepteelt, voorhanden gehad, te koop a<PERSON>eboden, verkocht, afgeleverd, verstrekt en vervoerd. Er ka....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3052, Rechtbank Midden-Nederland, 28-07-2022, 16/705044-17 (P).\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> heeft samen met een ander feitelijk leiding gegeven aan een groothandel, welk bedrijf een grote hoeveelheid voorwerpen en stoffen, die bestemd waren voor de illegale hennepteelt, voor<PERSON><PERSON> had, te koop heeft aangeboden, verk<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, verstrekt en vervoerd. Hi<PERSON><PERSON> heeft verdachte samen met een ander leiding gegeven aan een bedrijf dat zich ruim drie en een half jaar schuldig ....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2022:3053, Rechtbank Midden-Nederland, 28-07-2022, 16/705045-17 (P).\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> heeft samen met een ander feitelijk leiding gegeven aan een groothandel, welk bedrijf een grote hoeveelheid voorwerpen en stoffen, die bestemd waren voor de illegale hennepteelt, voor<PERSON><PERSON> had, te koop heeft aangeboden, verk<PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON>, verstrekt en vervoerd. Hi<PERSON><PERSON> heeft verdachte samen met een ander leiding gegeven aan een bedrijf dat zich ruim drie en een half jaar schuldig ....\n", "Full text: \n", "\n", "\n"]}], "source": ["query=\"kleinschalige wietplantage met slechts 10 planten, geen groothandel geen energiediefstal\"\n", "rechtspraak_service = RechtspraakSearchService()\n", "rechtspraak_service.update_project_context(project_context_str)\n", "relevant_legal_cases = await rechtspraak_service.search(query=query, return_ids_only=False)\n", "print(f\"Enchanced query used for semantic search: {rechtspraak_service.semantic_search_query}\\n\")\n", "relevant_legal_cases_context_str = rechtspraak_service.format_search_results_for_context_prompt(documents=relevant_legal_cases)\n", "print(relevant_legal_cases_context_str)"]}, {"cell_type": "markdown", "id": "6e3d0c5f-0624-4453-8e00-d49164c78224", "metadata": {}, "source": ["### Enhance query with project context "]}, {"cell_type": "code", "execution_count": 4, "id": "8c8b995e-b882-4e85-94c1-1d594d1a7e41", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["ERROR:asyncio:Unclosed client session\n", "client_session: <aiohttp.client.ClientSession object at 0x00000181B4FE6240>\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Enchanced query used for semantic search: geheimhoudingsovereenkomst Just4Safety Hooimeijer schending boetebeding juridische geschillen samenwerking coronasneltest subsidieaanvragen financiële afwikkeling aansprakelijkheid NexusLabor CONVAD bedrijfsarts samenwerkingsovereenkomst teststraten\n", "\n", "Answering request took  0.1 minutes\n", "Also consider the following 10 relevant legal cases:\n", "Case: ECLI:NL:RBGEL:2024:3508, Rechtbank Gelderland, 05-06-2024, 426586.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> met el<PERSON><PERSON> vier coronasneltestlocaties geëxploiteerd. <PERSON>  beëindigng van de samenwerking moet er tussen partijen worden afgerekend. Daarbij ontstaat het geschil. Eiseres vordert nog een bedrag van geda<PERSON>den, onder meer uit hoofde van schending van een geheimhoudingsovereenkomst. Die schending komt niet vast te staan. Ook de andere onderdelen van de vordering worden afgewezen..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNHO:2022:5738, Rechtbank Noord-Holland, 18-05-2022, 9536931.\n", "Case Summary: <PERSON><PERSON><PERSON> geheimhoudingsovereenkomst, onrechtmatige concurrentie.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBDHA:2022:15097, Rechtbank Den Haag, 07-12-2022, C/09/607435 / HA ZA 21-163.\n", "Case Summary: <PERSON><PERSON><PERSON> samenwerkingsovereenkomst en geheimhoudingsovereenkomst. Ook is sprake van een onrechtmatige daad. Eiseres heeft daardoor schade geleden. Toewijzing vorderingen. Verwijzing naar schadestaat. De gevorderde boete is niet toe<PERSON>..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2022:7077, Rechtbank Gelderland, 21-12-2022, C/05/387289 / HZ ZA 21-151.\n", "Case Summary: Samenwerkingsovereenkomst Covid19-testen en -teststraten. Borgstelling..\n", "Full text: \n", "\n", "Case: ECLI:NL:GHSHE:2014:1276, Gerechtshof 's-Hertogenbosch, 06-05-2014, HD 200.121.637_01.\n", "Case Summary: aansprakelijkheid commanditair vennoot, b<PERSON><PERSON><PERSON><PERSON>.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNHO:2023:4839, Rechtbank Noord-Holland, 08-03-2023, C/15/329685 / HA ZA 22-401.\n", "Case Summary: Samenwerkingsovereenkomst; geen schending geheimhoudingsbeding en geen schending relatiebeding..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBROT:2023:5031, Rechtbank Rotterdam, 14-06-2023, C/10/648166 / HA ZA 22-926.\n", "Case Summary: Schadevergoedingsrecht. Overheersende arbeidsverhouding. Aanknoping bij dwingendrechtelijk kader van art. 7:650 BW leidt tot nietigheid boetebeding in separate geheimhoudingsovereenkomst. Verweten gedragingen zijn wel onrechtmatig. Schadebegroting..\n", "Full text: \n", "\n", "Case: ECLI:NL:GHARL:2021:2476, Gerechtshof Arnhem-Lee<PERSON>en, 16-03-2021, 200.258.831/01.\n", "Case Summary: <PERSON><PERSON><PERSON><PERSON> over financiële afwikkeling samenwerkingsovereenkomst. Uitleg overeenkomst..\n", "Full text: \n", "\n", "Case: ECLI:NL:GHSHE:2017:3348, <PERSON>erechtshof 's-Hertogenbosch, 25-07-2017, 200 187 272_01.\n", "Case Summary: <PERSON><PERSON><PERSON> op boe<PERSON>beding in samenwerkingsovereenkomst vanwege het nalaten een gevelschildje te verwijderen nadat de overeenkomst was beëindigd. Matiging tot nihil..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2021:4326, Rechtbank Gelderland, 11-08-2021, C/05/376371 / HZ ZA 20-359.\n", "Case Summary: Financiële afwikkeling projectontwikkeling, uitleg samenwerkingsovereenkomst. Ontslag bestuurder is nietig..\n", "Full text: \n", "\n", "\n"]}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= \"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "project_context_str = cosmosdb.create_context_for_llm_prompt()\n", "\n", "query=\"\"\n", "rechtspraak_service = RechtspraakSearchService()\n", "rechtspraak_service.update_project_context(project_context_str=project_context_str)\n", "t0 = time()\n", "relevant_legal_cases = await rechtspraak_service.search(query=query, return_ids_only=False)\n", "print(f\"Enchanced query used for semantic search: {rechtspraak_service.semantic_search_query}\\n\")\n", "relevant_legal_cases_context_str = rechtspraak_service.format_search_results_for_context_prompt(documents=relevant_legal_cases)\n", "print(f\"Answering request took {(time() - t0) / 60: .1f} minutes\")\n", "print(relevant_legal_cases_context_str)"]}, {"cell_type": "code", "execution_count": null, "id": "1d2ddd12-3ffd-4e24-9388-84a0f10170c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "320656a4-8963-42df-90a4-0a6302db3573", "metadata": {}, "source": ["# Test ChatServices"]}, {"cell_type": "markdown", "id": "ee78e050-9c2d-46a7-a323-c88049f030b2", "metadata": {}, "source": ["### Organization only"]}, {"cell_type": "code", "execution_count": 3, "id": "645c2c8e-2879-4c82-85b2-b27889b401ad", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Op dit moment is er geen project actief en dus geen specifieke zaak waarvan een beschrijving kan worden gegeven. Daarnaast is het zoeken naar relevante jurisprudentie niet ingeschakeld. Om relevante jurisprudentie te vinden, moet je eerst jurisprudentie toevoegen als bron via het menu 'Bronnen'.\""]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id= None #\"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "\n", "chat = ChatService(organization_id=organization_id,\n", "        project_id=project_id,\n", "        cosmodb_client=cosmosdb)\n", "\n", "chat_request = ChatRequest(session_id=\"46b52c5a-8426-4835-b92d-e1f10276fa56\",\n", "            user_id=\"bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45\",\n", "            user_message=\"Can you give me a very short (3 sentences max) description of the current case? Also, did we already search for relevant case law or not?\")\n", "\n", "response = await chat.get_chat_response(chat_request=chat_request)\n", "response.content"]}, {"cell_type": "markdown", "id": "1eaba037-28e6-4430-a090-520eac8bf6da", "metadata": {}, "source": ["### With Project Context"]}, {"cell_type": "code", "execution_count": 4, "id": "17f6aa5f-2ae7-4745-a01d-6e3e90f1133a", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"De zaak betreft een geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V. over vermeende schending van een geheimhoudingsovereenkomst en financiële afwikkeling van hun samenwerking bij coronasnelteststraten. Hooimeijer stelt dat Just4Safety vertrouwelijke informatie heeft gebruikt voor concurrerende activiteiten en vordert schadevergoeding en boetes. Er is nog geen relevante jurisprudentie gezocht; dit kan worden gedaan door jurisprudentie toe te voegen via het menu 'Bronnen'.\""]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id=\"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id)\n", "\n", "chat = ChatService(organization_id=organization_id,\n", "        project_id=project_id,\n", "        cosmodb_client=cosmosdb)\n", "\n", "chat_request = ChatRequest(session_id=\"46b52c5a-8426-4835-b92d-e1f10276fa56\",\n", "            user_id=\"bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45\",\n", "            user_message=\"Can you give me a very short (3 sentences max) description of the current case? Also, did we already search for relevant case law or not?\")\n", "t0 = time()\n", "response = await chat.get_chat_response(chat_request=chat_request)\n", "print(f\"Answering request took {(time() - t0) / 60: .1f} minutes.\")\n", "print(response.content)"]}, {"cell_type": "markdown", "id": "e44a7df8-6c77-4cc5-81ea-83692146b540", "metadata": {}, "source": ["### With Project Context & Case Law Context"]}, {"cell_type": "code", "execution_count": 2, "id": "26ab102a-b302-43c8-b043-1c7e20ec3ea6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for Relevant Case Law\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\OneDrive - Metyis\\Documenten\\Personal Documents\\LegalPA\\statuta-rag-api-v2\\src\\app\\services\\rechtspraak_search_service.py:84: LangChainDeprecationWarning: The method `BaseChatModel.__call__` was deprecated in langchain-core 0.1.7 and will be removed in 1.0. Use invoke instead.\n", "  response = self._chat_client([\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Answering request took  0.1 minutes\n"]}, {"data": {"text/markdown": ["**Besch<PERSON><PERSON><PERSON> van de huidige zaak:**  \n", "De zaak betreft een geschil tussen Hooimeijer Holding B.V. en Just4Safety B.V. Hooimeijer Holding B.V. stelt dat Just4Safety B.V., <PERSON><PERSON> met <PERSON> en De Bruin, inbreuk heeft gemaakt op een geheimhoudingsovereenkomst door vertrouwelijke informatie en bedrijfsplannen van Hooimeijer te exploiteren via verschillende websites. Dit omvat onder andere het aanbieden van coronasneltesten op websites zoals www.just4medical.nl. Daarnaast wordt gesteld dat Just4Safety zich niet heeft gehouden aan de geheimhoudingsverplichting zoals opgenomen in hun algemene voorwaarden. Hooimeijer heeft conservatoir beslag gelegd op tegoeden van Just4Safety bij diverse banken ter zekerheidsstelling van haar vordering. De zaak omvat ook een discussie over de kosten van het beslag en de verantwoordelijkheid voor subsidieaanvragen en facturatie.\n", "\n", "**<PERSON><PERSON> naar relevante jurisprudentie:**  \n", "<PERSON>a, er is al gezocht naar relevante jurisprudentie. Er zijn tien juridische zaken geïdentificeerd die mogelijk relevant zijn voor de huidige zaak. Indien aanvullende jurisprudentie nodig is, kan deze worden toegevoegd via het menu 'Bronnen'."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["organization_id = \"46e06422-585a-4986-bf0e-f4ae9b84897e\"\n", "project_id=\"77344fb2-5676-49d4-8a9a-4d6454ee84cd\"\n", "document_ids = [UUID('82450b24-0cc6-4433-9a12-afff56a01991'), UUID('b0577af6-8866-4295-8b73-0d4c4a1dfd05')]\n", "cosmosdb = CosmosDBClient(organization_id=organization_id, project_id=project_id, document_ids=document_ids)\n", "\n", "chat = ChatService(\n", "    organization_id=organization_id,\n", "    project_id=project_id,          \n", "    cosmodb_client=cosmosdb)\n", "\n", "chat_request = ChatRequest(\n", "    session_id=\"46b52c5a-8426-4835-b92d-e1f10276fa56\",\n", "    user_id=\"bf31902f-bfa6-47c8-9f4e-daa1dc0c1a45\",\n", "    user_message=\"Can you give me a brief description of the current case? Also, did we already search for relevant case law or not?\",\n", "    jurisprudence=True)\n", "t0 = time()\n", "response = await chat.get_chat_response(chat_request=chat_request)\n", "print(f\"Answering request took {(time() - t0) / 60: .1f} minutes\")\n", "display(Markdown(response.content))"]}, {"cell_type": "code", "execution_count": 7, "id": "cf4727e9-dc13-4bb4-9879-f2286b0c5477", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Also consider the following 10 relevant legal cases:\n", "Case: ECLI:NL:RBDHA:2017:8922, Rechtbank Den Haag, 08-08-2017, 17/818.\n", "Case Summary: Beklag ex artikel 552a Sv gegrond verkla<PERSON>. Geen machtiging rechter-commissaris voor conservatoir beslag. Een onrechtmatig gelegd conservatoir beslag op grond van artikel 94a Sv kan niet worden omgezet in een beslag op grond van artikel 94 Sv..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBOVE:2024:4970, Rechtbank Overijssel, 26-09-2024, C/08/319010 / KG ZA 24-168.\n", "Case Summary: Opheffing conservatoir beslag. Beslag onnodig. Intellectuele eigendom. Duurzaamheid en verspilling. Begrip \"belanghebbende\" in de zin van artikel 705 Rv. Waarheidsplicht..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBNHO:2018:3577, Rechtbank Noord-Holland, 23-04-2018, 18/001883 en 18/001884.\n", "Case Summary: Beklag na conservatoir beslag in SFO. Onjuist is het standpunt dat het door de rechter-commissaris in de machtiging conservatoir beslag vermelde bedrag van € 339.819.16 een bovengrens voor de waarde van een beslag aangeeft. De wetgever heeft voor het conservatoir strafvorderlijk beslag de regeling van het conservatoir beslag in het Wetboek van Burgerlijke Rechtsvordering tot uitgangspunt genome....\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2020:1496, Rechtbank Gelderland, 19-02-2020, C/05/366382 / KG ZA 20-67.\n", "Case Summary: kort geding. Opheffen conservatoir beslag. Internationaal geschil. Conservatoir beslag is in Nederland gelegd door partij uit Kroatïe. Bevoegdheid rechtbank..\n", "Full text: \n", "\n", "Case: ECLI:NL:OGEAM:2016:49, <PERSON><PERSON><PERSON> in eerste aanleg van Sint Maarten, 09-08-2016, AR 2014/175.\n", "Case Summary: <PERSON><PERSON><PERSON>t. Aansprakelijkheid van het schip versus persoonlijke aansprakelijkheid van de kapitein. Stelplicht. Conservatoir beslag..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBALM:2012:BV1683, Rechtbank Almelo, 17-01-2012, 381826 CV EXPL 4497/11.\n", "Case Summary: Betreft onbetaalde factuur, die onvoldoende gemotiveerd is betwist. Conservatoir beslag niet onrechtmatig. In reconventie vordering kosten van conservatoir derdenbeslag afgewezen.\n", "Full text: \n", "\n", "Case: ECLI:NL:RBZWB:2023:7377, Rechtbank Zeeland-West-Brabant, 24-10-2023, C/02/412565 / KG ZA 23-388 (E).\n", "Case Summary: Vordering tot opheffing conservatoir beslag. Eisende partij heeft onvoldoende aannemelijk gemaakt dat de paardenwagen waarop het conservatoire beslag is gelegd zijn eigendo<PERSON> is. Vordering afgewezen..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2014:1974, Rechtbank Gelderland, 05-02-2014, 258624.\n", "Case Summary: Vordering tot opheffing van conservatoir beslag; onvoldoende aannemelijk dat geheimhoudingsovereenkomst is overtreden; summierlijk geble<PERSON> van de ondeugdelijkheid van de aan het beslag ten grondslag liggende vordering..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBGEL:2022:739, Rechtbank Gelderland, 10-02-2022, C/05/398563 / KG ZA 22-6.\n", "Case Summary: <PERSON><PERSON> geding. Opheffing conservatoir beslag. Conservatoir beslag is reeds van rechtswege vervallen art. 704 lid 2 Rv., maar eiser heeft nog steeds voldoende belang bij veroordeling van gedaagde tot opheffing van het beslag..\n", "Full text: \n", "\n", "Case: ECLI:NL:RBMNE:2021:6947, Rechtbank Midden-Nederland, 03-12-2021, C/16/528804 / KL ZA 21-271.\n", "Case Summary: Executiegeschil. Opheffen conservatoir beslag..\n", "Full text: \n", "\n", "\n"]}], "source": ["print(chat.relevant_legal_cases_context_str)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}