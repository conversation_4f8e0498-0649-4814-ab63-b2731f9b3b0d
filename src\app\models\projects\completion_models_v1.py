"""
This module defines models for completion context.
"""

# Standard imports
from enum import Enum
from typing import Optional, List
from uuid import UUID

# External imports
from pydantic import BaseModel, Field


class CompletionType(Enum):
    """
    Enumeration for the different types of prompts used in the completion services.
    """

    ADMINISTRATIVE_LAW_TIMELINE = "administrative_law_timeline"
    DESCRIPTION = "description"
    GOVERNMENT_JUDGMENT_SUMMARY = "government_judgment_summary"
    OBJECTION_ARGUMENTS = "objection_arguments"
    OBJECTION_DECISION = "objection_decision"
    OBJECTION_DECISION_DOCUMENT = "objection_decision_document"
    JUDGMENT = "judgment"
    QUESTIONS = "questions"
    STATEMENT_OF_DEFENCE = "statement_of_defence"
    STATEMENT_OF_DEFENCE_COUNTERCLAIMS = "statement_of_defence_counterclaims"
    STATEMENT_OF_DEFENCE_COUNTERCLAIM_CONTRADICTIONS = (
        "statement_of_defence_counterclaim_contradictions"
    )
    STATEMENT_OF_DEFENCE_COUNTERCLAIM_REQUESTS = (
        "statement_of_defence_counterclaim_requests"
    )
    STATEMENT_OF_DEFENCE_COUNTERCLAIM_SUMMARY = (
        "statement_of_defence_counterclaim_summary"
    )
    STATEMENT_OF_DEFENCE_CONCLUSION_AND_REQUESTS_TO_COURT = (
        "statement_of_defence_conclusion_and_requests_to_court"
    )
    STATEMENT_OF_DEFENCE_DEFENCE_AGAINST_CLAIM = (
        "statement_of_defence_defence_against_claim"
    )
    STATEMENT_OF_DEFENCE_EVIDENCE_SUBMISSION = (
        "statement_of_defence_evidence_submission"
    )
    STATEMENT_OF_DEFENCE_FACTUAL_DESCRIPTION_PARTY_POSITIONS = (
        "statement_of_defence_factual_description_party_positions"
    )
    STATEMENT_OF_DEFENCE_INTRODUCTION = "statement_of_defence_introduction"
    STATEMENT_OF_DEFENCE_LEGAL_GROUNDS = "statement_of_defence_legal_grounds"
    STATEMENT_OF_DEFENCE_LEGAL_GROUNDS_FOR_DEFENCE_AND_COUNTERCLAIMS = (
        "statement_of_defence_legal_grounds_for_defence_and_counterclaims"
    )
    STATEMENT_OF_DEFENCE_STRONG_POINTS = "statement_of_defence_strong_points"
    STATEMENT_OF_DEFENCE_WEAK_POINTS = "statement_of_defence_weak_points"
    STRONG_POINTS = "strong_points"
    DOCUMENT_SUMMARY = "summary"
    MULTIPLE_DOCUMENTS_SUMMARY = "summary"
    PROJECT_SUMMARY = "summary"
    TIMELINE = "timeline"
    WEAK_POINTS = "weak_points"
    WRIT_OF_SUMMONS_CONTRADICTIONS = "writ_of_summons_contradictions"
    WRIT_OF_SUMMONS_QUESTIONS = "writ_of_summons_questions"
    FOLLOW_UP_QUESTIONS = "follow_up_questions"
    WRIT_OF_SUMMONS_SUMMARY = "writ_of_summons_summary"
    WRIT_OF_SUMMONS_TIMELINE = "writ_of_summons_timeline"


class CompletionParameters(BaseModel):
    """
    Completion parameters.
    """

    completion_type: CompletionType = Field(
        ...,
        description="The completion type",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )


class CompletionOptions:
    """
    Completion options.
    """

    def __init__(self, completion_type: CompletionType, params: dict = None):
        """
        Initializes the completion options.

        Args:
            completion_type (CompletionType): The type of completion.
            params (dict): A dictionary of parameters for completion.
        """

        self.completion_type = completion_type
        self.params = params
        self._fill_missing_params()

    def _fill_missing_params(self) -> None:
        """
        Fills all missing parameters with an empty string.
        """

        if self.params:
            for key in self.params:
                if self.params[key] is None:
                    self.params[key] = ""


class ProjectCompletionResponse(BaseModel):
    """
    Model for the project completion response.
    """

    administrative_law_timeline: Optional[str] = Field(
        None,
        description="The administrative law project timeline.",
        example=(
            "### Timeline\n"
            "- Contract sent on YYYY-MM-DD\n"
            "- Contract signed on YYYY-MM-DD\n"
            "- Writ of summons sent on YYYY-MM-DD\n"
            "- Attempt to resolve conflict on YYYY-MM-DD\n"
            "- Deadlines on YYYY-MM-DD"
        ),
    )
    completion_tokens: int = Field(
        ...,
        description="The number of tokens used in the completion.",
        example=300,
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    description: Optional[str] = Field(
        None,
        description="The project description.",
        example=(
            "### Project Description\n\n"
            "This project involves the legal analysis of case documents related to a "
            "corporate lawsuit, including all relevant background information."
        ),
    )
    government_judgment_summary: Optional[str] = Field(
        None,
        description="The summary of the government judgment.",
        example=(
            "### Government Judgment Summary\n\n"
            "This summary provides an overview of the government judgment, including the key "
            "points of the decision and the legal implications for the parties involved."
        ),
    )
    id: str = Field(
        ...,
        description="An unique identifier provided by the LLM model.",
        example="run-f24be93a-f9e3-4bcb-8b31-66e5223d019a-0",
    )
    objection_arguments: Optional[str] = Field(
        None,
        description="The arguments in the objection.",
        example=(
            "### Objection Arguments\n"
            "- The objection is based on the lack of evidence supporting the claim.\n"
            "- The objection questions the validity of the presented documents."
        ),
    )
    objection_decision: Optional[str] = Field(
        None,
        description="The decision based on the objection.",
        example=(
            "### Objection Decision\n\n"
            "The objection is sustained due to insufficient evidence supporting the claim."
        ),
    )
    objection_decision_document: Optional[str] = Field(
        None,
        description="The decision document based on the objection.",
        example=(
            "### Objection Decision Document\n\n"
            "This document outlines the decision based on the objection raised by the defendant."
        ),
    )
    project_id: UUID = Field(
        ...,
        description="The project ID.",
        example="550e8400-e29b-41d4-a716-************",
    )
    judgment: Optional[str] = Field(
        None,
        description="The project judgment.",
        example=(
            "### Judgments\n"
            "Favorable: Based on the analysis of the provided documents, the evidence "
            "strongly supports the client's position, though potential counterclaims could "
            "present challenges."
        ),
    )
    prompt_tokens: int = Field(
        ...,
        description="The number of tokens used in the prompt.",
        example=200,
    )
    questions: Optional[str] = Field(
        None,
        description="The project questions.",
        example=(
            "### Project Questions\n"
            "- What is the current status of the lawsuit?\n"
            "- What are the next legal steps?\n"
            "- Are there any settlement offers on the table?"
        ),
    )
    strengths: Optional[str] = Field(
        None,
        description="The project strong points.",
        example=(
            "### Strong Points\n"
            "- The document provides a comprehensive analysis of the topic.\n"
            "- The arguments presented are well-supported with evidence."
        ),
    )
    statement_of_defence: Optional[str] = Field(
        None,
        description="The statement of defence.",
        example=(
            "### Statement of Defence\n"
            "The statement of defence outlines the defendant's arguments against the claims made by the claimant. "
            "It includes references to legal precedents, factual denials, and legal arguments supporting the defendant's position."
        ),
    )
    statement_of_defence_counterclaim_contradictions: Optional[str] = Field(
        None,
        description="The contradictions in the counterclaim.",
        example=(
            "### Statement of Defence in Counterclaim Contradictions\n"
            "The defendant highlights several contradictions within the claimant's allegations, focusing on inconsistencies between "
            "the claimant's statements and the available documentary evidence. The defendant contends that the claimant's assertion "
            "of damages is inflated, and the documents provided by the claimant contradict their claims of breach and negligence. "
        ),
    )
    statement_of_defence_counterclaim_summary: Optional[str] = Field(
        None,
        description="A summary of the statement of defence in counterclaim.",
        example=(
            "### Statement of Defence in Counterclaim Summary\n"
            "The counterclaims presented by the defendant challenge the original claims, arguing that the claimant's actions "
            "were unlawful, and offering legal and factual evidence that contradicts the claimant's position. The defendant "
            "argues that the counterclaims are valid and should be considered for dismissal of the original lawsuit."
        ),
    )
    statement_of_defence_counterclaims: Optional[str] = Field(
        None,
        description="The counterclaims in the statement of defence.",
        example=(
            "### Statement of Defence Counterclaims\n"
            "The defendant presents counterclaims, arguing that the claimant's actions were unlawful. "
            "The counterclaims include factual evidence and legal precedents supporting the defendant's position."
        ),
    )
    statement_of_defence_conclusion_and_requests_to_court: Optional[str] = Field(
        None,
        description="The conclusion and requests to the court in the statement of defence.",
        example=(
            "### Statement of Defence Conclusion and Requests to Court\n"
            "In conclusion, the defendant requests the court to dismiss the claim, grant judgment in favour of the defendant, "
            "and award legal costs incurred in defending the claim. The defendant submits that the claims are legally unfounded."
        ),
    )
    statement_of_defence_defence_against_claim: Optional[str] = Field(
        None,
        description="The statement of defence regarding the defence against the claim.",
        example=(
            "### Defence Against the Claim\n"
            "This section addresses the defendant's legal defence against the claims made by the claimant, "
            "highlighting legal precedents and factual inconsistencies."
        ),
    )
    statement_of_defence_evidence_submission: Optional[str] = Field(
        None,
        description="The evidence submission in the statement of defence.",
        example=(
            "### Statement of Defence Evidence Submission\n"
            "The defendant submits the following evidence to support their defence: documents, witness testimonies, and expert opinions, "
            "all of which contradict the claimant's allegations."
        ),
    )
    statement_of_defence_factual_description_party_positions: Optional[str] = Field(
        None,
        description="Factual description and positions of the parties in the case.",
        example=(
            "### Factual Description and Positions of the Parties\n"
            "This section outlines the positions of both parties, including detailed factual descriptions and "
            "the roles each party played in the conflict."
        ),
    )
    statement_of_defence_introduction: Optional[str] = Field(
        None,
        description="The introduction and summary of the conflict in the statement of defence.",
        example=(
            "### Introduction and Summary of the Conflict\n"
            "This section introduces the dispute, summarizing the claimant's key claims and the defendant's position. "
            "The relationship between the parties is outlined, including any relevant agreements and actions leading to the legal conflict. "
            "The defendant denies the claimant's allegations and provides a high-level explanation of the dispute's context."
        ),
    )
    statement_of_defence_legal_grounds_for_defence_and_counterclaims: Optional[str] = (
        Field(
            None,
            description="The legal grounds for the defence and counterclaims in the statement of defence.",
            example=(
                "### Statement of Defence Legal Grounds for Defence and Counterclaims\n"
                "The legal grounds for the defence and counterclaims are based on established case law, statutory provisions, "
                "and prior rulings that support the defendant's position and counterclaims against the claimant."
            ),
        )
    )
    statement_of_defence_legal_grounds: Optional[str] = Field(
        None,
        description="The legal grounds for the defence in the statement of defence.",
        example=(
            "### Statement of Defence Legal Grounds\n"
            "The legal grounds for the defence are based on established case law, statutory provisions, and prior rulings."
        ),
    )
    statement_of_defence_strengths: Optional[str] = Field(
        None,
        description="The statement of defence strong points.",
        example=(
            "### Defence Strengths\n"
            "- The defence effectively highlights key legal precedents that support the case.\n"
            "- Strong character references bolster the client's credibility.\n"
            "- The defence presents a logical and coherent argument that addresses potential counterclaims."
        ),
    )
    statement_of_defence_weaknesses: Optional[str] = Field(
        None,
        description="The statement of defence weak points.",
        example=(
            "### Defence Weaknesses\n"
            "- The defence fails to provide sufficient evidence to refute key claims made by the prosecution.\n"
            "- There are inconsistencies in the testimony of key witnesses.\n"
            "- The defence argument lacks sufficient legal precedents to support the claims made.\n"
            "- The defence has not addressed all the potential counterarguments raised by the prosecution."
        ),
    )
    statement_of_defence_witnesses: Optional[str] = Field(
        None,
        description="The witnesses mentioned in the statement of defence.",
        example=(
            "### Defence Witnesses\n"
            "- Witness 1: Provides testimony contradicting the claimant's version of events.\n"
            "- Witness 2: Offers expert opinion supporting the defence's legal arguments."
        ),
    )
    summary: Optional[str] = Field(
        None,
        description="The project summary.",
        example=(
            "### Project Summary\n\n"
            "This project involves the legal analysis of case documents related to a "
            "corporate lawsuit. The analysis includes reviewing contracts, correspondence, "
            "and court filings to identify key issues and potential outcomes."
        ),
    )
    timeline: Optional[str] = Field(
        None,
        description="The project timeline.",
        example=(
            "### Timeline\n"
            "- Contract sent on YYYY-MM-DD\n"
            "- Contract signed on YYYY-MM-DD\n"
            "- Writ of summons sent on YYYY-MM-DD\n"
            "- Attempt to resolve conflict on YYYY-MM-DD\n"
            "- Deadlines on YYYY-MM-DD"
        ),
    )
    total_tokens: int = Field(
        ...,
        description="The total number of tokens used.",
        example=500,
    )
    weaknesses: Optional[str] = Field(
        None,
        description="The project weak points.",
        example=(
            "### Weak Points\n"
            "- The document lacks a clear structure.\n"
            "- The conclusion is not well-developed."
        ),
    )
    writ_of_summons_contradictions: Optional[str] = Field(
        None,
        description="The writ of summons contradictions.",
        example=(
            "### Writ of Summons Contradictions\n\n"
            "This summary highlights the contradictions found within the writ of summons, "
            "detailing the conflicting points and discrepancies in the legal claims and statements made by the parties involved."
        ),
    )
    writ_of_summons_questions: Optional[str] = Field(
        None,
        description="The questions related to the writ of summons.",
        example=(
            "### Writ of Summons Questions\n"
            "- What claims are being made in the writ of summons?\n"
            "- Who are the parties involved in the case?\n"
            "- What evidence is presented to support the claims?"
        ),
    )

    follow_up_questions: Optional[str] = Field(
        None,
        description="The follow up questions based on the previous cycle.",
        example=(
            "### Follow up Questions\n"
            "- You didn't provide an answer to question 5. Can you still answer that?\n"
            "- For question 3 you said you transferred the money. Can you share a bankstatement?\n"
            "- You stated that they sent you an e-mail, what did it say?"
        ),
    )
    writ_of_summons_summary: Optional[str] = Field(
        None,
        description="The summary for the writ of summons.",
        example=(
            "### Writ of Summons Summary\n\n"
            "This summary provides an overview of the writ of summons, including the main "
            "points of the legal claim and the parties involved."
        ),
    )
    writ_of_summons_timeline: Optional[str] = Field(
        None,
        description="The writ of summons timeline of important events.",
        example=(
            "### Writ of Summons Timeline\n"
            "- Contract sent on YYYY-MM-DD\n"
            "- Contract signed on YYYY-MM-DD\n"
            "- Writ of summons sent on YYYY-MM-DD\n"
            "- Attempt to resolve conflict on YYYY-MM-DD\n"
            "- Deadlines on YYYY-MM-DD"
        ),
    )


class ProjectDocumentCompletionResponse(BaseModel):
    """
    Model for the document completion response.
    """

    id: str = Field(
        ...,
        description="An unique identifier provided by the LLM model.",
        example="run-*************-4965-9422-7e9e718e0671-0",
    )
    document_ids: List[UUID] = Field(
        ...,
        description="The document IDs.",
        example="[852d7f20-e7bd-4418-8307-76586db012ff,32A06FE7-98DA-4A88-B238-0C1D7553A9FB]",
    )
    project_id: UUID = Field(
        ...,
        description="The project ID.",
        example="550e8400-e29b-41d4-a716-************",
    )
    organization_id: UUID = Field(
        ...,
        description="The organization ID.",
        example="f38a52cd-b038-4092-b299-4ea72ab8cc22",
    )
    description: Optional[str] = Field(
        None,
        description="The document description.",
        example=(
            "### Document Description\n\n"
            "This document involves the legal analysis of a corporate process, including all relevant background information."
        ),
    )
    questions: Optional[str] = Field(
        None,
        description="The document questions.",
        example=(
            "### Document Questions\n"
            "- What is the current status of the lawsuit?\n"
            "- What are the next legal steps?\n"
            "- Are there any settlement offers on the table?"
        ),
    )
    strengths: Optional[str] = Field(
        None,
        description="The document strong points.",
        example=(
            "### Strong Points\n"
            "- The document provides a comprehensive analysis of the topic.\n"
            "- The arguments presented are well-supported with evidence."
        ),
    )
    summary: Optional[str] = Field(
        None,
        description="The document summary.",
        example=(
            "### Document Summary\n\n"
            "This document involves the legal analysis of case documents related to a "
            "corporate lawsuit. The analysis includes reviewing contracts, correspondence, "
            "and court filings to identify key issues and potential outcomes."
        ),
    )
    weaknesses: Optional[str] = Field(
        None,
        description="The document weak points.",
        example=(
            "### Weak Points\n"
            "- The document lacks a clear structure.\n"
            "- The conclusion is not well-developed."
        ),
    )
    prompt_tokens: int = Field(
        ...,
        description="The number of tokens used in the prompt.",
        example=20,
    )
    completion_tokens: int = Field(
        ...,
        description="The number of tokens used in the completion.",
        example=30,
    )
    total_tokens: int = Field(
        ...,
        description="The total number of tokens used.",
        example=50,
    )
