"""
This module defines FastAPI routers for project operations.
"""

# Standard imports
import json
from typing import Optional, AsyncGenerator
from uuid import UUID


from azure.core.exceptions import ResourceNotFoundError

# External imports
from fastapi import APIRouter, Header, Path, Query, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse, StreamingResponse
from opentelemetry.propagate import extract
from opentelemetry.trace import get_current_span, SpanKind, Status, StatusCode

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v1 import (
    get_logger,
    get_open_telemetry_tracer,
)

from src.app.models.documents.document_models_v1 import DocumentType
from src.app.models.projects.completion_models_v1 import (
    CompletionOptions,
    CompletionType,
    ProjectCompletionResponse,
)

from src.app.cosmos_db_client import CosmosDBClient
from src.app.services.completion.completion_services_v1 import ProjectCompletionService
from src.app.services.retrieval.retrieval_services_v1 import RetrieverService


api_tracer = get_open_telemetry_tracer()
api_logger = get_logger(__name__)
api_router = APIRouter()


async def stream_generator(
    completion_service: ProjectCompletionService,
    input_text: str,
    completion_options: CompletionOptions,
    top: Optional[int] = None,
) -> AsyncGenerator[str, None]:
    """
    Generates a stream of chat responses.
    Each chunk is formatted as JSON for easy parsing by the C# client.
    Args:
        completion_service (ProjectCompletionService): The completion service to use.
        input_text (str): The input text to generate completions from.
        completion_options (CompletionOptions): The completion options to use.
        top (Optional[int]): The number of completions to generate.
    Yields:
        StreamingChunk: A chunk of the AI response as it is generated
    Raises:
        HTTPException: If an error occurs during the streaming process.
    """

    try:
        async for chunk in completion_service.get_completion_stream(
            input_text, completion_options=completion_options
        ):
            # Convert chunk to JSON that C# can easily parse
            chunk_dict = chunk.model_dump()
            yield f"{json.dumps(chunk_dict)}\n"

    except ResourceNotFoundError as e:
        error_response = {
            "error": "ResourceNotFoundError",
            "message": str(e),
            "status_code": status.HTTP_404_NOT_FOUND,
        }
        yield f"{json.dumps(error_response)}\n"
        return

    except Exception as e:
        error_response = {
            "error": "InternalServerError",
            "message": str(e),
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        }
        yield f"{json.dumps(error_response)}\n"
        return


async def generate_response(
    organization_id: UUID,
    project_id: UUID,
    completion_options: CompletionOptions,
    document_type: Optional[DocumentType] = None,
    input_text: str = "*",
    use_streaming: bool = False,
) -> StreamingResponse | JSONResponse:
    """
    Generates a response based on the specified prompt type for a given project.

    This function:
     - retrieves the necessary documents for the project form the cosmo db
     - Performs semantic search for relevant law, jurisprudence and/or other materials using semantic search
     - formats all relevant materials into a context string to be included in the prompt template
     - generates a completion using the specified prompt type, and returns the result as a JSON response.
    """

    try:

        cosmodb_client = CosmosDBClient(
            organization_id=organization_id,
            project_id=project_id
        )

        retriever_service = RetrieverService(
            organization_id=organization_id,
            project_id=project_id,
            document_type=document_type.name if document_type else None,
        )

        completion_service = ProjectCompletionService(cosmodb_client=cosmodb_client,
                                                      retriever_service=retriever_service)
        if use_streaming:
            return StreamingResponse(
                stream_generator(
                    completion_service,
                    input_text=input_text,
                    completion_options=completion_options,
                ),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Content-Type": "text/event-stream",
                    "X-Accel-Buffering": "no",
                },
            )
        completion_results = await completion_service.get_completion(
            input_text=input_text,
            completion_options=completion_options,
        )

        # The field will be in the response model with the generated content
        content_field = get_content_field(completion_options.completion_type)
        response_data = ProjectCompletionResponse(
            id=completion_results.id,
            project_id=project_id,
            organization_id=organization_id,
            **{content_field: completion_results.content},
            prompt_tokens=completion_results.response_metadata["token_usage"][
                "prompt_tokens"
            ],
            completion_tokens=completion_results.response_metadata["token_usage"][
                "completion_tokens"
            ],
            total_tokens=completion_results.response_metadata["token_usage"][
                "total_tokens"
            ],
        )

        return JSONResponse(
            content=jsonable_encoder(response_data),
            status_code=status.HTTP_200_OK,
        )

    except Exception as e:
        api_logger.error(
            "Error generating response for organization %s in project %s",
            organization_id,
            project_id,
            exc_info=True,
        )

        current_span = get_current_span()
        if current_span:
            current_span.set_status(Status(StatusCode.ERROR, str(e)))

        # Raise so the caller (route) can handle it
        raise e


def get_content_field(completion_type: CompletionType) -> str:
    """
    Maps a completion type to the corresponding field in the project response model.

    Args:
        completion_type (CompletionType): The type of completion to be mapped.

    Returns:
        str: The name of the field in the response model where the generated content
        will be placed.
    """

    content_field_mapping = {
        CompletionType.ADMINISTRATIVE_LAW_TIMELINE: "administrative_law_timeline",
        CompletionType.DESCRIPTION: "description",
        CompletionType.FOLLOW_UP_QUESTIONS: "follow_up_questions",
        CompletionType.GOVERNMENT_JUDGMENT_SUMMARY: "government_judgment_summary",
        CompletionType.OBJECTION_ARGUMENTS: "objection_arguments",
        CompletionType.OBJECTION_DECISION: "objection_decision",
        CompletionType.OBJECTION_DECISION_DOCUMENT: "objection_decision_document",
        CompletionType.JUDGMENT: "judgment",
        CompletionType.QUESTIONS: "questions",
        CompletionType.STATEMENT_OF_DEFENCE: "statement_of_defence",
        CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIM_CONTRADICTIONS: "statement_of_defence_counterclaim_contradictions",
        CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIM_SUMMARY: "statement_of_defence_counterclaim_summary",
        CompletionType.PROJECT_SUMMARY: "summary",
        CompletionType.STRONG_POINTS: "strengths",
        CompletionType.TIMELINE: "timeline",
        CompletionType.WEAK_POINTS: "weaknesses",
    }

    return content_field_mapping[completion_type]


@api_router.get(
    "/{organization_id}/projects/{project_id}/judgment",
    description="Generates a predicted judgement based on all documents in a project.",
    operation_id="generate_project_judgment",
    response_description="The generated judgment for the project.",
    response_model=ProjectCompletionResponse,
    summary="Judgments for a project. ⚠️",
)
async def generate_project_judgment(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates a predicted judgement based on all documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated summary.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.JUDGMENT,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "predicted judgment")


@api_router.get(
    "/{organization_id}/projects/{project_id}/questions",
    description="Generates questions based on a specific project.",
    operation_id="generate_project_questions",
    response_description="The generated questions for the project.",
    response_model=ProjectCompletionResponse,
    summary="Questions for a project. ☑️",
)
async def generate_project_questions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates questions based on a specific project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated questions.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.QUESTIONS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "questions")


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence",
    description="Generates the statement of defence for a project, based on the provided documents.",
    operation_id="generate_statement_of_defence",
    response_description="The generated statement of defence for the project.",
    response_model=ProjectCompletionResponse,
    summary="Statement of defence for a project. ☑️",
    tags=["statement of defence"],
)
async def generate_statement_of_defence(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates the statement of defence for a project based on the provided documents.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: A response containing the generated statement of defence for the project.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "statement of defence")


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence_counterclaim/contradictions",
    description="Generates contradictions based on the statement of defence in counterclaim documents in a project.",
    operation_id="generate_statement_of_defence_counterclaim_contradictions",
    response_description="The generated contradictions for the statement of defence in counterclaim.",
    response_model=ProjectCompletionResponse,
    summary="Contradictions for statement of defence in counterclaim. ⚠️",
    tags=["statement of defence in counterclaim"],
)
async def generate_statement_of_defence_counterclaim_contradictions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates contradictions based on all statement of defence in counterclaim documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated contradictions for the statement of defence in counterclaim.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIM_CONTRADICTIONS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                DocumentType.STATEMENT_OF_DEFENCE_COUNTERCLAIM,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "contradictions")


@api_router.get(
    "/{organization_id}/projects/{project_id}/statement_of_defence_counterclaim/summary",
    description="Generates a summary of the statement of defence in counterclaim for a project.",
    operation_id="generate_statement_of_defence_counterclaim_summary",
    response_description="The generated summary of the statement of defence in counterclaim.",
    response_model=ProjectCompletionResponse,
    summary="Summary of the statement of defence in counterclaim. ⚠️",
    tags=["statement of defence in counterclaim"],
)
async def generate_statement_of_defence_counterclaim_summary(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates a summary of the statement of defence with counterclaims for a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: A response containing the generated summary of the statement of defence in counterclaim for the project.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STATEMENT_OF_DEFENCE_COUNTERCLAIM_SUMMARY,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                DocumentType.STATEMENT_OF_DEFENCE_COUNTERCLAIM,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e,
            organization_id,
            project_id,
            "statement of defence in counterclaim summary",
        )


@api_router.get(
    "/{organization_id}/projects/{project_id}/strong_points",
    description="Generates strong points based on all documents in a project.",
    operation_id="generate_project_strong_points",
    response_description="The generated strong points for the project.",
    response_model=ProjectCompletionResponse,
    summary="Strong points for a project. ☑️",
)
async def generate_project_strong_points(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates strong points based on all documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated strong points.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.STRONG_POINTS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "strong points")


@api_router.get(
    "/{organization_id}/projects/{project_id}/summary",
    description="Generates a summary for a specific project.",
    operation_id="generate_project_summary",
    response_description="The generated summary for the project.",
    response_model=ProjectCompletionResponse,
    summary="Summary for a project. ☑️",
)
async def generate_project_summary(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates a summary for a specific project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated summary.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.PROJECT_SUMMARY,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "summary")


@api_router.get(
    "/{organization_id}/projects/{project_id}/timeline",
    description="Generates a timeline based on all documents in a project.",
    operation_id="generate_project_timeline",
    response_description="The generated timeline for the project.",
    response_model=ProjectCompletionResponse,
    summary="Timeline for a project. ️",
)
async def generate_project_timeline(
    request: Request,
    organization_id: UUID,
    project_id: UUID,
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates a timeline for a specific project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated timeline.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.TIMELINE,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "timeline")


@api_router.get(
    "/{organization_id}/projects/{project_id}/administrative_law/timeline",
    description="Generates a timeline based on all documents in an administrative law project.",
    operation_id="generate_administrative_law_timeline",
    response_description="The generated timeline for the administrative law project.",
    response_model=ProjectCompletionResponse,
    summary="Timeline for an administrative law project. ⚠️",
)
async def generate_administrative_law_timeline(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> JSONResponse:
    """
    Generates a timeline for a specific administrative law project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated timeline.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await generate_response(
                organization_id,
                project_id,
                CompletionOptions(CompletionType.ADMINISTRATIVE_LAW_TIMELINE),
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, "administrative law timeline"
        )


@api_router.get(
    "/{organization_id}/projects/{project_id}/weak_points",
    description="Generates weak points based on all documents in a project.",
    operation_id="generate_project_weak_points",
    response_description="The generated weak points for the project.",
    response_model=ProjectCompletionResponse,
    summary="Weak points for a project. ☑️",
)
async def generate_project_weak_points(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates weak points based on all documents in a project.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated weak points.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.WEAK_POINTS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )

            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "weak points")


@api_router.get(
    "/{organization_id}/projects/{project_id}/follow_up_questions",
    description="Generates questions based on the given answers and additional documents",
    operation_id="generate_follow_up_questions",
    response_description="The generated follow up questions based on the previous cycle.",
    response_model=ProjectCompletionResponse,
    summary="Follow up questions for the new cycle",
    tags=["questions"],
)
async def generate_follow_up_questions(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
    claimant: str = Header(None, alias="X-Claimant-Name"),
    defendant: str = Header(None, alias="X-Defendant-Name"),
) -> JSONResponse:
    """
    Generates follow up questions based on the previous cycle.

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated follow up questions for the current cycle.
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            completion_options = CompletionOptions(
                CompletionType.FOLLOW_UP_QUESTIONS,
                params={
                    "claimant": claimant,
                    "defendant": defendant,
                },
            )
            return await generate_response(
                organization_id,
                project_id,
                completion_options,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "follow up questions")


@api_router.get(
    "/{organization_id}/projects/{project_id}/government_judgment/summary",
    description="Generates a summary of the judgment made by the government.",
    operation_id="generate_government_judgment_summary",
    response_description="The generated summary of the judgment of the government",
    response_model=ProjectCompletionResponse,
    summary="Summary for the government judgment. ☑⚠️",
    tags=["government judgment"],
)
async def generate_government_judgment_summary(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> JSONResponse:
    """
    Generates a summary of the decision made by the government

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated summary
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await generate_response(
                organization_id,
                project_id,
                CompletionOptions(CompletionType.GOVERNMENT_JUDGMENT_SUMMARY),
                DocumentType.GOVERNMENT_JUDGMENT,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, "government judgment summary"
        )


@api_router.get(
    "/{organization_id}/projects/{project_id}/objection/arguments",
    description="Generates an overview of the arguments in the objection.",
    operation_id="generate_objection_arguments",
    response_description="The generated argument analysis of the objection",
    response_model=ProjectCompletionResponse,
    summary="Overview of the arguments. ⚠️️",
    tags=["objection"],
)
async def generate_objection_arguments_overview(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> JSONResponse:
    """
    Generates an overview of the arguments in the objection

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated overview
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await generate_response(
                organization_id,
                project_id,
                CompletionOptions(CompletionType.OBJECTION_ARGUMENTS),
                DocumentType.OBJECTION,
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "objection arguments")


@api_router.get(
    "/{organization_id}/projects/{project_id}/objection/decision",
    description="Generates a suggested decision based on the objection.",
    operation_id="generate_objection_decision",
    response_description="The generated decision based on the objection",
    response_model=ProjectCompletionResponse,
    summary="A suggested decision. ☑⚠️",
    tags=["objection"],
)
async def generate_objection_decision(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> JSONResponse:
    """
    Generates a suggested decision based on the objection

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated decision
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await generate_response(
                organization_id,
                project_id,
                CompletionOptions(CompletionType.OBJECTION_DECISION),
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(e, organization_id, project_id, "objection decision")


@api_router.get(
    "/{organization_id}/projects/{project_id}/objection/decision_document",
    description="Generates a response based on the decision of the objection.",
    operation_id="generate_objection_decision_document",
    response_description="The generated decision document based on the decision",
    response_model=ProjectCompletionResponse,
    summary="A suggested response. ⚠️️",
    tags=["objection"],
)
async def generate_objection_decision_document(
    request: Request,
    organization_id: UUID = Path(..., description="The ID of the organization."),
    project_id: UUID = Path(..., description="The ID of the project."),
    use_streaming: bool = Query(False, description="Flag to use streaming response"),
) -> JSONResponse:
    """
    Generates a suggested response based on the decision and the objection

    Args:
        organization_id (UUID): The ID of the organization.
        project_id (UUID): The ID of the project.

    Returns:
        JSONResponse: The generated decision document
    """

    try:
        with api_tracer.start_as_current_span(
            name=request.scope["endpoint"].__name__,
            context=extract(request.headers),
            kind=SpanKind.SERVER,
        ):
            return await generate_response(
                organization_id,
                project_id,
                CompletionOptions(CompletionType.OBJECTION_DECISION_DOCUMENT),
                use_streaming=use_streaming,
            )

    except Exception as e:
        return _handle_error(
            e, organization_id, project_id, "objection decision document"
        )


def _handle_error(e: Exception, organization_id: UUID, project_id: UUID, action: str):
    """
    Handles the error by logging it and raising an HTTPException with a suitable status code.

    Args:
        e (Exception): The exception that was raised.
        organization_id (UUID): The ID of the organization that caused the error.
        project_id (UUID): The ID of the project that caused the error.
        action (str): A string describing the action being attempted (e.g., 'description', 'questions').

    Raises:
        HTTPException: Raises an appropriate HTTP error.
    """

    # Log the error with additional context
    api_logger.error(
        "Error generating %s for organization %s in project %s",
        action,
        organization_id,
        project_id,
        exc_info=True,
    )

    if isinstance(e, ResourceNotFoundError):
        api_logger.error(
            e.message,
            exc_info=True,
        )

        return JSONResponse(
            content={
                "error": "ResourceNotFoundError",
                "message": e.message,
                "status_code": status.HTTP_404_NOT_FOUND,
            },
            status_code=status.HTTP_404_NOT_FOUND,
        )

    # Raise a generic 500 error for unexpected failures, preserving the original exception
    return JSONResponse(
        content={
            "error": "InternalServerError",
            "message": f"Error generating {action} for organization {organization_id} in project {project_id}.",
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
        },
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
    )
