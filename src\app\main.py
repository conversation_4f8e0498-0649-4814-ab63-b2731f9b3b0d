"""
Module to create and configure a FastAPI application.
This module defines the FastAPI application, includes
the necessary routers, and creates the OpenAPI schema.
"""

# Standard imports
import logging
import os
import uvicorn

from enum import Enum
from typing import Any, Dict

# External imports
from dotenv import find_dotenv, load_dotenv
from fastapi import FastAPI
from fastapi.logger import logger as fastapi_logger
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.openapi.utils import get_openapi
from fastapi.responses import RedirectResponse
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

# Load environment variables (needs to happen prior to imports)
load_dotenv(find_dotenv())

# Internal imports
from src.app.configurations.telemetry.telemetry_configs_v1 import configure_telemetry
from src.app.routers.chat import chat_router_v1
from src.app.routers.documents import document_router_v1
from src.app.routers.projects import project_router_v1
from src.app.routers.projects.statement_of_defence import statement_of_defence_router_v1
from src.app.routers.projects.writ_of_summons import writ_of_summons_router_v1
from src.app.routers.retrieval import retrieval_router_v1, vector_store_router_v1
from src.app.routers.system import health_router_v1
from src.app.routers.projects.rechtspraak_router_v1 import router as rechtspraak_router_v1


class ResourceTags(str, Enum):
    """
    Enum that defines the tags for various API resources.
    """

    CHAT_COMPLETIONS = "chat completions"
    """
    Tag for operations related to chat completions.
    """

    DOCUMENTS = "documents"
    """
    Tag for operations related to document completions.
    """

    HEALTHZ = "healthz"
    """
    Tag for operations related to service health, liveness, and readiness.
    """

    PROJECTS = "projects"
    """
    Tag for operations related to project completions.
    """

    RETRIEVERS = "retrievers"
    """
    Tag for operations related to data retrieval or search functionality.
    """

    STATEMENT_OF_DEFENCE = "statement of defence"
    """
    Tag for operations related to statement of defence.
    """

    VECTOR_STORES = "vector stores"
    """
    Tag for operations related to vector-based storage systems.
    """

    WRIT_OF_SUMMONS = "writ of summons"
    """
    Tag for operations related to writ of summons.
    """


def create_app() -> FastAPI:
    """
    Creates and configures the FastAPI application.

    This function initializes the FastAPI app, sets up the API prefix, includes various
    routers for different application modules (health, documents, projects, etc.),
    and sets up the OpenAPI schema for API documentation.

    Returns:
        FastAPI: The configured FastAPI application.
    """

    app = FastAPI(logger=fastapi_logger)

    # Default API prefix is '/api/rag' (can be overridden with environment variable)
    api_prefix = os.getenv("API_PREFIX", "/api")

    # Include versioned routers for different modules
    include_versioned_routers(app, api_prefix)

    # Set up OpenAPI schema and middleware options
    app.openapi = lambda: create_openapi(app)

    # Handles compression for standard and streaming responses
    gzip_minimum_size = int(
        os.getenv("GZIP_MINIMUM_SIZE", "1000")
    )  # default 1000 bytes

    gzip_compress_lvl = int(os.getenv("GZIP_COMPRESS_LEVEL", "6"))

    app.add_middleware(
        GZipMiddleware,
        minimum_size=gzip_minimum_size,
        compresslevel=gzip_compress_lvl,
    )

    # Enforces trusted hosts
    trusted_allowed_hosts = os.getenv(
        "ALLOWED_HOSTS", "localhost,127.0.0.1,*.azurewebsites.net,*.statuta.ai"
    ).split(",")

    app.add_middleware(
        CORSMiddleware,
        allow_origins=trusted_allowed_hosts,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Enforces HTTPS incoming requests
    # app.add_middleware(HTTPSRedirectMiddleware)

    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=trusted_allowed_hosts,
    )

    return app


def include_versioned_routers(app: FastAPI, api_prefix: str):
    """
    Includes versioned routers for different modules to the FastAPI app.

    Args:
        app (FastAPI): The FastAPI application instance.
        api_prefix (str): The API prefix to use for the routes.
    """
    versions = [
        "",
        "v1",
        "latest",
    ]  # TODO: Remove magical strings and refactor this out in next version.

    for version in versions:
        version_prefix = f"/{version}" if version else ""

        # Include the routers with versioned prefixes
        app.include_router(
            chat_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.CHAT_COMPLETIONS],
        )
        app.include_router(
            document_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.DOCUMENTS],
        )
        app.include_router(
            health_router_v1.api_router,
            prefix=f"/api{version_prefix}",
            tags=[ResourceTags.HEALTHZ],
        )
        app.include_router(
            project_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.PROJECTS],
        )
        app.include_router(
            statement_of_defence_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.PROJECTS, ResourceTags.STATEMENT_OF_DEFENCE],
        )
        app.include_router(
            retrieval_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.RETRIEVERS],
        )
        app.include_router(
            vector_store_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.VECTOR_STORES],
        )
        app.include_router(
            writ_of_summons_router_v1.api_router,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.PROJECTS, ResourceTags.WRIT_OF_SUMMONS],
        )
        app.include_router(
            rechtspraak_router_v1,
            prefix=f"{api_prefix}{version_prefix}/rag",
            tags=[ResourceTags.PROJECTS, "rechtspraak"],
        )


def create_openapi(app: FastAPI) -> Dict[str, Any]:
    """
    Creates the OpenAPI Specification (OAS 3.1) schema for the API.

    This function generates the OpenAPI schema, including basic information like title, version,
    description, and routes. It also adds logo and contact information to the schema.

    Args:
        app (FastAPI): The FastAPI app instance used to generate the schema.

    Returns:
        dict: The OpenAPI schema dictionary.
    """

    # If OpenAPI schema already exists, return it
    if app.openapi_schema:
        return app.openapi_schema

    # Generate the OpenAPI schema
    openapi_schema = get_openapi(
        title="Statuta RAG | REST API",
        version="v1.0.80-buildX",
        routes=app.routes,
        summary="Statuta RAG API Reference",
        description=(
            "Generate conclusions, provide summaries, and manage embeddings "
            "for projects and given inputs. These outputs are generated "
            "using retrieval-augmented generation, leveraging Azure AI services."
        ),
        tags=[
            {
                "name": ResourceTags.CHAT_COMPLETIONS.value,
                "description": "Operations related to chat completions.",
            },
            {
                "name": ResourceTags.DOCUMENTS.value,
                "description": "Operations related to documents.",
            },
            {
                "name": ResourceTags.HEALTHZ.value,
                "description": "Operations related to service health, liveness, and readiness.",
            },
            {
                "name": ResourceTags.PROJECTS.value,
                "description": "Operations related to projects.",
            },
            {
                "name": ResourceTags.RETRIEVERS.value,
                "description": "Operations related to retrievers.",
            },
            {
                "name": ResourceTags.STATEMENT_OF_DEFENCE.value,
                "description": "Operations related to statement of defence.",
            },
            {
                "name": ResourceTags.VECTOR_STORES.value,
                "description": "Operations related to vector stores.",
            },
            {
                "name": ResourceTags.WRIT_OF_SUMMONS.value,
                "description": "Operations related to writ of summons.",
            },
        ],
    )

    openapi_schema["info"]["x-logo"] = {
        "url": "https://www.hawkia.com.br/assets/images/hwk-img-logo-horizontal-neg-escuro.svg"
    }

    openapi_schema["info"]["contact"] = {
        "name": "Support",
        "url": "https://www.hawkia.com.br/contato",
        "email": "<EMAIL>",
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Create and configure FastAPI app
fastapi_app = create_app()

fastapi_logger.setLevel(
    os.getenv("LOG_LEVEL_DEFAULT", logging.getLevelName(logging.WARNING))
)  # Configure log level

# Configure telemetry (logging, tracing, and Azure Monitor)
configure_telemetry(logger=fastapi_logger)

# Instrument FastAPI with OpenTelemetry for monitoring
FastAPIInstrumentor.instrument_app(fastapi_app)


# Ensure that any request to the root of the API is redirected to /docs
@fastapi_app.get("/", include_in_schema=False)
async def docs_redirect():
    """
    Redirects the user automatically to the API documentation (/docs).

    Returns:
        RedirectResponse: Redirects to the "/docs" endpoint.
    """
    return RedirectResponse(url="/docs")
