import logging
import os

from azure.cosmos import CosmosClient
from azure.core.exceptions import ResourceNotFoundError
from dotenv import find_dotenv, load_dotenv
from typing import List, Optional
from uuid import UUID

from opentelemetry.sdk.resources import CONTAINER_NAME

from src.app.models.documents.document_models_v1 import DocumentType
from langchain_core.documents.base import Document
from src.app.services.retrieval.retrieval_services_v1 import (
    RetrieverService,
    RetrieverSearchType,
)


def _create_context_prompt_from_document(doc: dict) -> List[str]:
    """Create list containing a pure text representation each paragraph in a document, including the document
    name, page number & paragraph number for use in the project context of a prompt to an llm """

    paragraphed_document_list = []
    paragraphs = doc['Paragraphs']
    if paragraphs is None:
        return [""]
    else:
        for paragraph in paragraphs:
            if paragraph is not None:
                paragraph_number_in_document = paragraph['GlobalIndex']
                paragraph_number_on_page = paragraph['IndexOnPage']
                page = paragraph['StartingPage']
                text = paragraph['Content']
                multipage = paragraph['SpansMultiplePages']
                paragraph_str = \
                    f"Paragraph {paragraph_number_on_page} on page {page} in document {doc['Name']}:\n{text}\n"
                paragraphed_document_list.append(paragraph_str)

    return paragraphed_document_list


def _create_context_prompt_from_multiple_documents(documents: List[dict]) -> str:
    """Get a pure text representation for a set of user uploaded documents to be used as a context in a prompt to an
    LLM"""
    paragraphed_documents = '\n'.join([f" {''.join(_create_context_prompt_from_document(doc))}"
        for doc in documents])

    if len(documents) == 0:
        documents_context_str = ''
    elif len(documents) == 1:
        documents_context_str = \
            f"""
            Consider the following document related to the current case that the user has provided: 

            {paragraphed_documents}\n"""
    else:
        documents_context_str = \
            f"""
            Consider the following {len(documents)} documents related to the current case that the user has provided: 

            {paragraphed_documents}\n"""

    # TODO: AIAI-33 add check on the total context length. Return context as list with strings in case of very long
    #  context and iterate or first condense somehow. Maybe not at this point as we also gather other bits of context
    #  like from case law. Should only be done just before the final context is being fed to an LLM
    if len(documents_context_str):
        documents_context_str = documents_context_str[0:300_000]

    return documents_context_str


def _process_retriever_output(results: List[Document]) -> List[dict]:
    """
    TO BE REMOVED, FOR BACKWARDS COMPATIBILITY ONLY
    Transform the output of the retriever into the new format that we expect from the cosmos db"""

    # Get all unique document_ids
    seen = set()
    document_ids_in_result = [r.metadata['document_id'] for r in results if
                              not (r.metadata['document_id'] in seen or seen.add(r.metadata['document_id']))]

    # Order each document by page and return list of ordered pages
    document_pages_ordered = []
    for doc_id in document_ids_in_result:
        doc_pages = [doc for doc in results if doc.metadata['document_id'] == doc_id]
        doc_pages_ordered = sorted(
            doc_pages,
            key=lambda r: r.metadata.get('page', float('inf'))  # fallback to put missing pages at end
        )
    document_pages_ordered += doc_pages_ordered

    documents_processed = []
    for page in document_pages_ordered:
        dict_result = {'id': page.metadata['id'],
                       'OrganizationId': page.metadata['organization_id'],
                       'ProjectId': page.metadata['project_id'],
                       'DocumentId': page.metadata.get('document_id', ''),
                       'Name': page.metadata.get('source', 'unkown'),
                       'PageCount': page.metadata.get('total_pages', 1)
        }

        words = page.page_content.split()
        paragraph_size = 120
        paragraphs_text = [' '.join(words[i:i + paragraph_size]) for i in range(0, len(words), paragraph_size)]
        paragraphs = []
        for idx, par in enumerate(paragraphs_text):
            paragraph_dict = {
                'GlobalIndex':idx+1,
                'IndexOnPage': idx+1,
                'StartingPage': page.metadata['page'] + 1 if isinstance(page.metadata.get('page', ''), int) else float('inf'),
                'Content': par,
                'SpansMultiplePages': False}
            paragraphs.append(paragraph_dict)
        dict_result['Paragraphs'] = paragraphs
        documents_processed.append(dict_result)
    return documents_processed


class CosmosDBClient :
    """
    Service for accessing (anonymized) user uploaded documents split into paragraphs from the azure cosmos db
    """

    def __init__(
        self,
        organization_id: UUID,
        project_id: UUID,
        document_ids: Optional[List[UUID]] = None,
    ) -> None:
        self.organization_id = organization_id
        self.project_id = project_id
        self.document_ids = document_ids
        self.n_documents = 0
        self.legalpa_cosmosdb_endpoint = os.getenv("AZURE_LEGALPA_COSMOS_ENDPOINT")
        self.legalpa_cosmosdb_key = os.getenv("AZURE_LEGALPA_COSMOS_KEY")
        self._init_db_client()
        self._init_retriever() # For backwards compatibility, should be removed

    def _init_db_client(self):
        if self.project_id:
            try:
                # Initialize Azure Cosmos db client to check if project exists
                self._legalpa_cosmos_client = CosmosClient(self.legalpa_cosmosdb_endpoint, self.legalpa_cosmosdb_key)
                database_name = 'org-' + str(self.organization_id)
                
                self._database = self._legalpa_cosmos_client.get_database_client(database_name)
                container_name = 'prj-' + str(self.project_id)
                self.container = self._database.get_container_client(container_name)
                if self.container is None:
                    raise ResourceNotFoundError(
                        f"Container for project {self.project_id} in organization {self.organization_id} not found."
                    )
                    
            except Exception:
                # Raise error once we remove the old code that gets project from the retriever that is still included
                # here for backwards compatibility
                #raise ResourceNotFoundError(
                #    f"Container for project {self.project_id} in organization {self.organization_id} not found."
                #)
                logging.info(f"Container for project {self.project_id} in organization {self.organization_id} not found. "
                      f"Falling back to using the retriever")
                self._legalpa_cosmos_client = None
        else:
            self._legalpa_cosmos_client = None

    def _init_retriever(self):
        """TO BE REMOVED, FOR BACKWARDS COMPATIBILITY ONLY. Get project documents from the vector store if we cant
        find them in the cosmos db"""
        try:
            documents_ids = [] if self.document_ids is None else self.document_ids
            self._retriever = RetrieverService(
                organization_id=self.organization_id,
                project_id=self.project_id,
                include_jurisprudence=False,
                document_ids=documents_ids,
            )
        except ResourceNotFoundError as e:
            logging.info(f"Retriever for project {self.project_id} in organization {self.organization_id} not found.")

    def get_all_documents(self) -> List[dict]:
        """Get all documents in current project """
        if self._legalpa_cosmos_client is not None:
            query = f"SELECT * FROM c"

            # TODO: ensure that it is only getting user uploaded documents, not sure if other materials (like generated
            #  summaries and timelines are also stored in the cosmosdb in the same container. If so, they should not
            #  be retrieved at this point. Filter for 'Category': 'ClientInformation'? Check once backend ingests into
            #  the cosmos db
            documents = list(self.container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))
        else:
            # To be removed, for backwards compatibility only
            results = self._retriever.search_project(query="*", top_k=None, search_type=RetrieverSearchType.VECTOR)
            documents = _process_retriever_output(results=results)
        self.n_documents = len(documents)
        return documents

    def get_documents(self, document_ids=List[UUID]):
        """Get specific documents from the current project based on their id"""
        if self._legalpa_cosmos_client is not None:
            document_id_list_str = ', '.join([f"\'{(id)}\'" for id in document_ids]) if document_ids is not None else ''
            query = f"SELECT * FROM c WHERE c.id in ({document_id_list_str})"
            documents = list(self.container.query_items(
                query=query,
                enable_cross_partition_query=True
            ))
        else:
            # To be removed, for backwards compatibility only
            results = self._retriever.search_project(query="*", top_k=None, search_type=RetrieverSearchType.VECTOR)
            document_id_list_str = [str(doc_id) for doc_id in self.document_ids]
            results = [i for i in results if i.metadata['document_id'] in document_id_list_str]
            documents = _process_retriever_output(results=results)
            self.n_documents = len(documents)
        return documents

    def create_context_for_llm_prompt(self) -> str:
        """Retrieve paragraph-splitted (anonymized) documents from the project and format them to create a single
        string containing the full context that can be inlcuded in a single prompt to an LLM"""

        if self.project_id is None:
            return 'The user is not currently chatting with a project.'

        if self.document_ids:
            documents = self.get_documents(document_ids=self.document_ids)
        else:
            documents = self.get_all_documents()

        full_context_str = _create_context_prompt_from_multiple_documents(documents=documents)

        # Escape { and } for use in f-strings
        full_context_str = full_context_str.replace("{", "{{").replace("}", "}}")

        return full_context_str

    def create_context_for_iterative_llm_prompting(self) -> List[str]:
        """Retrieve a list of paragraph-splitted (anonymized) documents from the project and format them to create a
        list of strings that can be iteratively fed to a sequence of LLM prompts for chunk-wise processing"""

        if self.project_id is None:
            return ['The user is not currently chatting with a project.']

        if self.document_ids:
            documents = self.get_documents(document_ids=self.document_ids)
        else:
            documents = self.get_all_documents()

        list_of_context_by_paragraphs = []
        for doc in documents:
            list_of_context_by_paragraphs += _create_context_prompt_from_document(doc)
        return list_of_context_by_paragraphs
